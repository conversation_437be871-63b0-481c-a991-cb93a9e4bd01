"""
Management command to load Units of Measure from Odoo data
"""

import json
import os
from django.core.management.base import BaseCommand
from django.db import transaction
from django.contrib.auth import get_user_model
from sales.models import ProductUomCategory, ProductUom

User = get_user_model()


class Command(BaseCommand):
    help = 'Load Units of Measure from Odoo data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--update',
            action='store_true',
            help='Update existing UOMs',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Loading Units of Measure from Odoo data...'))
        
        # Load UOM categories from extracted JSON file
        categories_file = 'uom_categories_data.json'
        uoms_file = 'uoms_data.json'
        
        if not os.path.exists(categories_file) or not os.path.exists(uoms_file):
            self.stdout.write(
                self.style.ERROR(
                    f'UOM data files not found. '
                    'Please run: python extract_odoo_data.py first'
                )
            )
            return
        
        with open(categories_file, 'r') as f:
            categories_data = json.load(f)
            
        with open(uoms_file, 'r') as f:
            uoms_data = json.load(f)

        created_categories = 0
        created_uoms = 0
        updated_categories = 0
        updated_uoms = 0
        
        # Get or create admin user for create_uid/write_uid
        admin_user, created = User.objects.get_or_create(
            username='admin',
            defaults={'email': '<EMAIL>', 'is_staff': True, 'is_superuser': True}
        )
        
        with transaction.atomic():
            # First, load categories
            self.stdout.write('Loading UOM categories...')
            for category_data in categories_data:
                # Extract name from nested structure
                if isinstance(category_data['name'], dict):
                    name = category_data['name'].get('en_US', 'Unknown')
                else:
                    name = category_data['name']
                
                # Prepare clean data for Django model
                clean_data = {
                    'name': name,
                    'create_uid': admin_user,
                    'write_uid': admin_user,
                }
                
                try:
                    category = ProductUomCategory.objects.get(name=name)
                    if options['update']:
                        # Update existing category
                        for field, value in clean_data.items():
                            if field not in ['create_uid']:  # Don't update create_uid
                                setattr(category, field, value)
                        category.save()
                        updated_categories += 1
                        self.stdout.write(f'Updated category: {name}')
                    else:
                        self.stdout.write(f'Category already exists: {name}')
                        
                except ProductUomCategory.DoesNotExist:
                    # Create new category
                    category = ProductUomCategory.objects.create(**clean_data)
                    created_categories += 1
                    self.stdout.write(f'Created category: {name}')
            
            # Then, load UOMs
            self.stdout.write('\nLoading UOMs...')
            for uom_data in uoms_data:
                # Extract name from nested structure
                if isinstance(uom_data['name'], dict):
                    name = uom_data['name'].get('en_US', 'Unknown')
                else:
                    name = uom_data['name']
                
                # Extract category name
                if isinstance(uom_data['category_name'], dict):
                    category_name = uom_data['category_name'].get('en_US', 'Unknown')
                else:
                    category_name = uom_data['category_name']
                
                # Get category
                try:
                    category = ProductUomCategory.objects.get(name=category_name)
                except ProductUomCategory.DoesNotExist:
                    self.stdout.write(f'Category not found: {category_name}, skipping UOM: {name}')
                    continue
                
                # Calculate factor_inv (inverse of factor)
                factor = uom_data.get('factor', 1.0)
                factor_inv = 1.0 / factor if factor != 0 else 1.0
                
                # Prepare clean data for Django model
                clean_data = {
                    'name': name,
                    'category_id': category,
                    'factor': factor,
                    'factor_inv': factor_inv,
                    'uom_type': uom_data.get('uom_type', 'reference'),
                    'active': uom_data.get('active', True),
                    'create_uid': admin_user,
                    'write_uid': admin_user,
                }
                
                try:
                    uom = ProductUom.objects.get(name=name, category_id=category)
                    if options['update']:
                        # Update existing UOM
                        for field, value in clean_data.items():
                            if field not in ['create_uid']:  # Don't update create_uid
                                setattr(uom, field, value)
                        uom.save()
                        updated_uoms += 1
                        self.stdout.write(f'Updated UOM: {name} ({category_name})')
                    else:
                        self.stdout.write(f'UOM already exists: {name} ({category_name})')
                        
                except ProductUom.DoesNotExist:
                    # Create new UOM
                    uom = ProductUom.objects.create(**clean_data)
                    created_uoms += 1
                    self.stdout.write(f'Created UOM: {name} ({category_name})')

        # Summary
        self.stdout.write(
            self.style.SUCCESS(
                f'\nUOM loading completed!\n'
                f'Categories - Created: {created_categories}, Updated: {updated_categories}\n'
                f'UOMs - Created: {created_uoms}, Updated: {updated_uoms}\n'
                f'Total categories in system: {ProductUomCategory.objects.count()}\n'
                f'Total UOMs in system: {ProductUom.objects.count()}'
            )
        )
        
        # Show UOMs by category
        self.stdout.write('\nUOMs by category:')
        for category in ProductUomCategory.objects.all().order_by('name'):
            uom_count = ProductUom.objects.filter(category_id=category, active=True).count()
            if uom_count > 0:
                uoms = ProductUom.objects.filter(category_id=category, active=True)
                uom_list = ', '.join([uom.name for uom in uoms])
                self.stdout.write(f'  {category.name}: {uom_count} UOMs ({uom_list})')
