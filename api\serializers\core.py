from rest_framework import serializers
from core.models import Company, Partner, Currency, Country


class CountrySerializer(serializers.ModelSerializer):
    """
    Serializer for Country model.
    """
    class Meta:
        model = Country
        fields = ['id', 'name', 'code', 'create_date', 'write_date']
        read_only_fields = ['create_date', 'write_date']


class CurrencySerializer(serializers.ModelSerializer):
    """
    Serializer for Currency model.
    """
    class Meta:
        model = Currency
        fields = ['id', 'name', 'symbol', 'decimal_places', 'full_name', 'active', 'create_date', 'write_date']
        read_only_fields = ['create_date', 'write_date']


class CompanySerializer(serializers.ModelSerializer):
    """
    Serializer for Company model.
    """
    currency_name = serializers.CharField(source='currency_id.name', read_only=True)
    country_name = serializers.CharField(source='country_id.name', read_only=True)
    
    class Meta:
        model = Company
        fields = [
            'id', 'name', 'email', 'phone', 'website', 'vat',
            'street', 'street2', 'city', 'zip',
            'currency_id', 'currency_name', 'country_id', 'country_name',
            'create_date', 'write_date'
        ]
        read_only_fields = ['create_date', 'write_date']
    
    def validate_email(self, value):
        """Validate email format"""
        if value and '@' not in value:
            raise serializers.ValidationError("Enter a valid email address.")
        return value


class PartnerSerializer(serializers.ModelSerializer):
    """
    Serializer for Partner model.
    """
    country_name = serializers.CharField(source='country_id.name', read_only=True)
    parent_name = serializers.CharField(source='parent_id.name', read_only=True)
    full_address = serializers.SerializerMethodField()
    partner_type_display = serializers.CharField(source='get_partner_type_display', read_only=True)
    
    class Meta:
        model = Partner
        fields = [
            'id', 'name', 'email', 'phone', 'mobile', 'website', 'vat',
            'street', 'street2', 'city', 'zip',
            'country_id', 'country_name', 'parent_id', 'parent_name',
            'is_company', 'supplier_rank', 'customer_rank',
            'full_address', 'active', 'create_date', 'write_date'
        ]
        read_only_fields = ['create_date', 'write_date']
    
    def get_full_address(self, obj):
        """Get formatted full address"""
        address_parts = []
        
        if obj.street:
            address_parts.append(obj.street)
        if obj.street2:
            address_parts.append(obj.street2)
        if obj.city:
            address_parts.append(obj.city)
        if obj.state:
            address_parts.append(obj.state)
        if obj.zip:
            address_parts.append(obj.zip)
        if obj.country_id:
            address_parts.append(obj.country_id.name)
        
        return ', '.join(address_parts)
    
    def validate_email(self, value):
        """Validate email format"""
        if value and '@' not in value:
            raise serializers.ValidationError("Enter a valid email address.")
        return value
    
    def validate(self, attrs):
        """Custom validation"""
        # Ensure company partners have appropriate ranks
        if attrs.get('is_company', False):
            if not attrs.get('supplier_rank', 0) and not attrs.get('customer_rank', 0):
                attrs['customer_rank'] = 1  # Default to customer
        
        return attrs


class PartnerCreateSerializer(PartnerSerializer):
    """
    Serializer for creating partners with additional validation.
    """
    def create(self, validated_data):
        """Create partner with proper defaults"""
        # Set create_uid and write_uid from request user
        request = self.context.get('request')
        if request and request.user:
            validated_data['create_uid'] = request.user
            validated_data['write_uid'] = request.user
        
        return super().create(validated_data)


class PartnerUpdateSerializer(PartnerSerializer):
    """
    Serializer for updating partners.
    """
    def update(self, instance, validated_data):
        """Update partner with proper write_uid"""
        # Set write_uid from request user
        request = self.context.get('request')
        if request and request.user:
            validated_data['write_uid'] = request.user
        
        return super().update(instance, validated_data)


class PartnerListSerializer(serializers.ModelSerializer):
    """
    Lightweight serializer for partner lists.
    """
    country_name = serializers.CharField(source='country_id.name', read_only=True)
    
    class Meta:
        model = Partner
        fields = [
            'id', 'name', 'email', 'phone', 'city', 'country_name',
            'is_company', 'supplier_rank', 'customer_rank', 'active'
        ]


class CompanyCreateSerializer(CompanySerializer):
    """
    Serializer for creating companies with additional validation.
    """
    def create(self, validated_data):
        """Create company with proper defaults"""
        # Set create_uid and write_uid from request user
        request = self.context.get('request')
        if request and request.user:
            validated_data['create_uid'] = request.user
            validated_data['write_uid'] = request.user
        
        return super().create(validated_data)


class CurrencyCreateSerializer(CurrencySerializer):
    """
    Serializer for creating currencies with additional validation.
    """
    def create(self, validated_data):
        """Create currency with proper defaults"""
        # Set create_uid and write_uid from request user
        request = self.context.get('request')
        if request and request.user:
            validated_data['create_uid'] = request.user
            validated_data['write_uid'] = request.user
        
        return super().create(validated_data)


class CountryCreateSerializer(CountrySerializer):
    """
    Serializer for creating countries with additional validation.
    """
    def create(self, validated_data):
        """Create country with proper defaults"""
        # Set create_uid and write_uid from request user
        request = self.context.get('request')
        if request and request.user:
            validated_data['create_uid'] = request.user
            validated_data['write_uid'] = request.user
        
        return super().create(validated_data)
