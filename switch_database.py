#!/usr/bin/env python
"""
Database Switcher for Django ERP
Allows switching between SQLite and PostgreSQL
"""

import os
import sys
import subprocess

def run_command(command, check=True):
    """Run a shell command and return the result"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, check=check)
        return result.stdout.strip(), result.stderr.strip()
    except subprocess.CalledProcessError as e:
        return None, e.stderr

def test_postgresql_connection():
    """Test if PostgreSQL is available and accessible"""
    print("🔍 Testing PostgreSQL connection...")
    
    try:
        import psycopg2
        conn = psycopg2.connect(
            host='localhost',
            port='5432',
            database='django_erp',
            user='ERPUser',
            password='ERPUser',
            connect_timeout=5
        )
        conn.close()
        print("✅ PostgreSQL connection successful")
        return True
    except ImportError:
        print("❌ psycopg2 not installed. Run: pip install psycopg2-binary")
        return False
    except Exception as e:
        print(f"❌ PostgreSQL connection failed: {e}")
        return False

def switch_to_postgresql():
    """Switch to PostgreSQL database"""
    print("\n🔄 Switching to PostgreSQL...")
    
    if not test_postgresql_connection():
        print("❌ Cannot switch to PostgreSQL. Please set it up first.")
        return False
    
    # Set environment variable
    os.environ['USE_POSTGRESQL'] = 'true'
    
    # Run migrations
    print("🔄 Running migrations...")
    stdout, stderr = run_command('python manage.py migrate')
    if stdout:
        print("✅ Migrations completed successfully")
        print("🎉 Switched to PostgreSQL database!")
        return True
    else:
        print(f"❌ Migration failed: {stderr}")
        return False

def switch_to_sqlite():
    """Switch to SQLite database"""
    print("\n🔄 Switching to SQLite...")
    
    # Set environment variable
    os.environ['USE_POSTGRESQL'] = 'false'
    
    # Run migrations
    print("🔄 Running migrations...")
    stdout, stderr = run_command('python manage.py migrate')
    if stdout:
        print("✅ Migrations completed successfully")
        print("🎉 Switched to SQLite database!")
        return True
    else:
        print(f"❌ Migration failed: {stderr}")
        return False

def show_current_database():
    """Show current database configuration"""
    print("\n📊 Current Database Configuration:")
    
    # Check environment variable
    use_pg = os.environ.get('USE_POSTGRESQL', 'true').lower() == 'true'
    
    if use_pg:
        print("🐘 Database: PostgreSQL")
        print("📍 Host: localhost:5432")
        print("🗄️  Database: django_erp")
        print("👤 User: ERPUser")
        
        if test_postgresql_connection():
            print("🟢 Status: Connected")
        else:
            print("🔴 Status: Not accessible")
    else:
        print("🗃️  Database: SQLite")
        print("📁 File: db.sqlite3")
        print("🟢 Status: Available")

def install_postgresql_guide():
    """Show PostgreSQL installation guide"""
    print("\n📖 PostgreSQL Installation Guide:")
    print("=" * 40)
    print("1. Download PostgreSQL:")
    print("   https://www.postgresql.org/download/")
    print()
    print("2. Install with these settings:")
    print("   - Port: 5432")
    print("   - Superuser password: (remember this!)")
    print("   - Install all components")
    print()
    print("3. After installation, run:")
    print("   psql -U postgres -h localhost")
    print()
    print("4. Create database and user:")
    print("   CREATE DATABASE django_erp;")
    print("   CREATE USER ERPUser WITH PASSWORD 'ERPUser';")
    print("   GRANT ALL PRIVILEGES ON DATABASE django_erp TO ERPUser;")
    print("   ALTER USER ERPUser CREATEDB;")
    print()
    print("5. Install Python adapter:")
    print("   pip install psycopg2-binary")
    print()
    print("6. Run this script again to switch to PostgreSQL")

def main():
    """Main function"""
    print("🚀 Django ERP Database Switcher")
    print("=" * 40)
    
    while True:
        print("\nOptions:")
        print("1. Show current database")
        print("2. Switch to PostgreSQL")
        print("3. Switch to SQLite")
        print("4. Test PostgreSQL connection")
        print("5. PostgreSQL installation guide")
        print("6. Exit")
        
        choice = input("\nEnter your choice (1-6): ").strip()
        
        if choice == '1':
            show_current_database()
        elif choice == '2':
            switch_to_postgresql()
        elif choice == '3':
            switch_to_sqlite()
        elif choice == '4':
            test_postgresql_connection()
        elif choice == '5':
            install_postgresql_guide()
        elif choice == '6':
            print("👋 Goodbye!")
            break
        else:
            print("❌ Invalid choice. Please try again.")

if __name__ == "__main__":
    # Change to Django project directory
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    main()
