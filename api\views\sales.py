from rest_framework import viewsets, serializers
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

# Placeholder serializer for API documentation
class SalesPlaceholderSerializer(serializers.Serializer):
    message = serializers.CharField(default="This endpoint is not yet implemented")

# Placeholder for sales API views
# TODO: Implement full sales API

class SaleOrderViewSet(viewsets.ViewSet):
    permission_classes = [IsAuthenticated]
    serializer_class = SalesPlaceholderSerializer

    def list(self, request):
        return Response({"message": "Sales Order API endpoints will be implemented soon"})

class SaleOrderLineViewSet(viewsets.ViewSet):
    permission_classes = [IsAuthenticated]
    serializer_class = SalesPlaceholderSerializer

    def list(self, request):
        return Response({"message": "Sales Order Line API endpoints will be implemented soon"})

class ProductPricelistViewSet(viewsets.ViewSet):
    permission_classes = [IsAuthenticated]
    serializer_class = SalesPlaceholderSerializer

    def list(self, request):
        return Response({"message": "Pricelist API endpoints will be implemented soon"})
