# Generated by Django 4.2.21 on 2025-07-20 13:21

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('core', '0004_rename_state_countrystate_countrygroup'),
    ]

    operations = [
        migrations.CreateModel(
            name='HrContractType',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(help_text='Contract Type', max_length=255)),
                ('sequence', models.IntegerField(default=10)),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='HrDepartment',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(help_text='Department Name', max_length=255)),
                ('active', models.BooleanField(default=True)),
                ('note', models.TextField(blank=True, help_text='Additional Information')),
                ('color', models.IntegerField(default=0, help_text='Color Index')),
                ('child_ids', models.ManyToManyField(blank=True, related_name='parent_departments', to='hr.hrdepartment')),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='HrEmployee',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(help_text='Employee Name', max_length=255)),
                ('active', models.BooleanField(default=True)),
                ('private_name', models.CharField(blank=True, help_text='Private Name', max_length=255)),
                ('gender', models.CharField(blank=True, choices=[('male', 'Male'), ('female', 'Female'), ('other', 'Other')], max_length=10)),
                ('marital', models.CharField(blank=True, choices=[('single', 'Single'), ('married', 'Married'), ('cohabitant', 'Legal Cohabitant'), ('widower', 'Widower'), ('divorced', 'Divorced')], max_length=20)),
                ('birthday', models.DateField(blank=True, null=True)),
                ('private_email', models.EmailField(blank=True, help_text='Private Email', max_length=254)),
                ('phone', models.CharField(blank=True, max_length=50)),
                ('mobile_phone', models.CharField(blank=True, max_length=50)),
                ('employee_type', models.CharField(choices=[('employee', 'Employee'), ('student', 'Student'), ('trainee', 'Trainee'), ('contractor', 'Contractor'), ('freelance', 'Freelancer')], default='employee', max_length=20)),
                ('private_street', models.CharField(blank=True, max_length=255)),
                ('private_street2', models.CharField(blank=True, max_length=255)),
                ('private_city', models.CharField(blank=True, max_length=100)),
                ('private_zip', models.CharField(blank=True, max_length=20)),
                ('work_location', models.CharField(blank=True, max_length=255)),
                ('work_email', models.EmailField(blank=True, max_length=254)),
                ('work_phone', models.CharField(blank=True, max_length=50)),
                ('employee_number', models.CharField(blank=True, help_text='Employee ID', max_length=50)),
                ('passport_id', models.CharField(blank=True, help_text='Passport No', max_length=50)),
                ('bank_account_id', models.CharField(blank=True, help_text='Bank Account', max_length=100)),
                ('emergency_contact', models.CharField(blank=True, max_length=255)),
                ('emergency_phone', models.CharField(blank=True, max_length=50)),
                ('identification_id', models.CharField(blank=True, help_text='Identification No', max_length=50)),
                ('ssnid', models.CharField(blank=True, help_text='SSN', max_length=50)),
                ('sinid', models.CharField(blank=True, help_text='SIN', max_length=50)),
                ('notes', models.TextField(blank=True)),
                ('color', models.IntegerField(default=0, help_text='Color Index')),
                ('is_address_home_a_company', models.BooleanField(default=False)),
                ('coach_id', models.ForeignKey(blank=True, help_text='Coach', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='coached_employees', to='hr.hremployee')),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('department_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='hr.hrdepartment')),
            ],
        ),
        migrations.CreateModel(
            name='HrLeaveType',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(help_text='Leave Type', max_length=255)),
                ('active', models.BooleanField(default=True)),
                ('sequence', models.IntegerField(default=100)),
                ('allocation_type', models.CharField(choices=[('no', 'No Allocation'), ('fixed', 'Fixed Allocation'), ('fixed_allocation', 'Fixed by HR')], default='no', max_length=20)),
                ('request_unit', models.CharField(choices=[('day', 'Day'), ('half_day', 'Half Day'), ('hour', 'Hour')], default='day', max_length=20)),
                ('validation_type', models.CharField(choices=[('no_validation', 'No Validation'), ('hr', 'By HR Officer'), ('manager', 'By Employee Manager'), ('both', 'By Employee Manager then HR Officer')], default='hr', max_length=20)),
                ('max_leaves', models.FloatField(default=0.0, help_text='Maximum Allowed')),
                ('leaves_taken', models.FloatField(default=0.0, help_text='Leaves Already Taken')),
                ('color_name', models.CharField(blank=True, max_length=50)),
                ('color', models.IntegerField(default=0)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='HrLeave',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(help_text='Description', max_length=255)),
                ('active', models.BooleanField(default=True)),
                ('request_date_from', models.DateField(help_text='Request Start Date')),
                ('request_date_to', models.DateField(help_text='Request End Date')),
                ('request_hour_from', models.CharField(blank=True, help_text='Request Start Hour', max_length=10)),
                ('request_hour_to', models.CharField(blank=True, help_text='Request End Hour', max_length=10)),
                ('request_unit_hours', models.BooleanField(default=False, help_text='Custom Hours')),
                ('request_unit_half', models.BooleanField(default=False, help_text='Half Day')),
                ('request_unit_custom', models.BooleanField(default=False, help_text='Custom')),
                ('number_of_days', models.FloatField(default=0.0, help_text='Number of Days')),
                ('duration_display', models.CharField(blank=True, max_length=255)),
                ('state', models.CharField(choices=[('draft', 'To Submit'), ('confirm', 'To Approve'), ('refuse', 'Refused'), ('validate1', 'Second Approval'), ('validate', 'Approved'), ('cancel', 'Cancelled')], default='draft', max_length=20)),
                ('notes', models.TextField(blank=True, help_text='Add a reason...')),
                ('date_from', models.DateTimeField(blank=True, help_text='Start Date', null=True)),
                ('date_to', models.DateTimeField(blank=True, help_text='End Date', null=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('employee_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='leave_ids', to='hr.hremployee')),
                ('holiday_status_id', models.ForeignKey(help_text='Leave Type', on_delete=django.db.models.deletion.PROTECT, to='hr.hrleavetype')),
                ('manager_id', models.ForeignKey(blank=True, help_text='Manager', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='managed_leaves', to='hr.hremployee')),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='HrJob',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(help_text='Job Title', max_length=255)),
                ('active', models.BooleanField(default=True)),
                ('state', models.CharField(choices=[('recruit', 'Recruitment in Progress'), ('open', 'Not Recruiting')], default='open', max_length=20)),
                ('no_of_recruitment', models.IntegerField(default=1, help_text='Expected New Employees')),
                ('no_of_hired_employee', models.IntegerField(default=0, help_text='Hired Employees')),
                ('description', models.TextField(blank=True, help_text='Job Description')),
                ('requirements', models.TextField(blank=True, help_text='Job Requirements')),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('contract_type_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='hr.hrcontracttype')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('department_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='hr.hrdepartment')),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.AddField(
            model_name='hremployee',
            name='job_id',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='hr.hrjob'),
        ),
        migrations.AddField(
            model_name='hremployee',
            name='parent_id',
            field=models.ForeignKey(blank=True, help_text='Manager', null=True, on_delete=django.db.models.deletion.SET_NULL, to='hr.hremployee'),
        ),
        migrations.AddField(
            model_name='hremployee',
            name='private_country_id',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='employee_private_country', to='core.country'),
        ),
        migrations.AddField(
            model_name='hremployee',
            name='private_state_id',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='employee_private_state', to='core.countrystate'),
        ),
        migrations.AddField(
            model_name='hremployee',
            name='user_id',
            field=models.OneToOneField(blank=True, help_text='Related User', null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='hremployee',
            name='write_uid',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='hrdepartment',
            name='manager_id',
            field=models.ForeignKey(blank=True, help_text='Department Manager', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='managed_departments', to='hr.hremployee'),
        ),
        migrations.AddField(
            model_name='hrdepartment',
            name='parent_id',
            field=models.ForeignKey(blank=True, help_text='Parent Department', null=True, on_delete=django.db.models.deletion.CASCADE, to='hr.hrdepartment'),
        ),
        migrations.AddField(
            model_name='hrdepartment',
            name='write_uid',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL),
        ),
        migrations.CreateModel(
            name='HrContract',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(help_text='Contract Reference', max_length=255)),
                ('active', models.BooleanField(default=True)),
                ('date_start', models.DateField(help_text='Start Date')),
                ('date_end', models.DateField(blank=True, help_text='End Date', null=True)),
                ('state', models.CharField(choices=[('draft', 'New'), ('open', 'Running'), ('close', 'Expired'), ('cancel', 'Cancelled')], default='draft', max_length=20)),
                ('wage', models.DecimalField(decimal_places=2, default=0.0, help_text='Wage', max_digits=20)),
                ('resource_calendar_id', models.CharField(blank=True, help_text='Working Schedule', max_length=255)),
                ('trial_date_end', models.DateField(blank=True, help_text='End of Trial Period', null=True)),
                ('notes', models.TextField(blank=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('currency_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='core.currency')),
                ('department_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='hr.hrdepartment')),
                ('employee_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='contract_ids', to='hr.hremployee')),
                ('job_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='hr.hrjob')),
                ('type_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='hr.hrcontracttype')),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='HrAttendance',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('check_in', models.DateTimeField(help_text='Check In')),
                ('check_out', models.DateTimeField(blank=True, help_text='Check Out', null=True)),
                ('worked_hours', models.FloatField(default=0.0, help_text='Worked Hours')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('employee_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attendance_ids', to='hr.hremployee')),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.AddIndex(
            model_name='hrleavetype',
            index=models.Index(fields=['name'], name='hr_hrleavet_name_d53a96_idx'),
        ),
        migrations.AddIndex(
            model_name='hrleavetype',
            index=models.Index(fields=['company_id'], name='hr_hrleavet_company_ab8c5b_idx'),
        ),
        migrations.AddIndex(
            model_name='hrleavetype',
            index=models.Index(fields=['sequence'], name='hr_hrleavet_sequenc_f820de_idx'),
        ),
        migrations.AddConstraint(
            model_name='hrleavetype',
            constraint=models.UniqueConstraint(fields=('name', 'company_id'), name='hr_leave_type_name_company_uniq'),
        ),
        migrations.AddIndex(
            model_name='hrleave',
            index=models.Index(fields=['employee_id'], name='hr_hrleave_employe_8f8333_idx'),
        ),
        migrations.AddIndex(
            model_name='hrleave',
            index=models.Index(fields=['holiday_status_id'], name='hr_hrleave_holiday_aceb9f_idx'),
        ),
        migrations.AddIndex(
            model_name='hrleave',
            index=models.Index(fields=['state'], name='hr_hrleave_state_0d4373_idx'),
        ),
        migrations.AddIndex(
            model_name='hrleave',
            index=models.Index(fields=['request_date_from'], name='hr_hrleave_request_ff16d3_idx'),
        ),
        migrations.AddIndex(
            model_name='hrleave',
            index=models.Index(fields=['request_date_to'], name='hr_hrleave_request_bc4be0_idx'),
        ),
        migrations.AddIndex(
            model_name='hrleave',
            index=models.Index(fields=['company_id'], name='hr_hrleave_company_06cf8f_idx'),
        ),
        migrations.AddConstraint(
            model_name='hrleave',
            constraint=models.CheckConstraint(check=models.Q(('request_date_to__gte', models.F('request_date_from'))), name='hr_leave_valid_dates'),
        ),
        migrations.AddConstraint(
            model_name='hrleave',
            constraint=models.CheckConstraint(check=models.Q(('number_of_days__gte', 0)), name='hr_leave_positive_days'),
        ),
        migrations.AddIndex(
            model_name='hrjob',
            index=models.Index(fields=['name'], name='hr_hrjob_name_e4e681_idx'),
        ),
        migrations.AddIndex(
            model_name='hrjob',
            index=models.Index(fields=['department_id'], name='hr_hrjob_departm_2f3330_idx'),
        ),
        migrations.AddIndex(
            model_name='hrjob',
            index=models.Index(fields=['company_id'], name='hr_hrjob_company_fb6a4a_idx'),
        ),
        migrations.AddIndex(
            model_name='hrjob',
            index=models.Index(fields=['state'], name='hr_hrjob_state_040a53_idx'),
        ),
        migrations.AddConstraint(
            model_name='hrjob',
            constraint=models.CheckConstraint(check=models.Q(('no_of_recruitment__gte', 0)), name='hr_job_positive_recruitment'),
        ),
        migrations.AddConstraint(
            model_name='hrjob',
            constraint=models.CheckConstraint(check=models.Q(('no_of_hired_employee__gte', 0)), name='hr_job_positive_hired'),
        ),
        migrations.AddIndex(
            model_name='hremployee',
            index=models.Index(fields=['name'], name='hr_hremploy_name_1feada_idx'),
        ),
        migrations.AddIndex(
            model_name='hremployee',
            index=models.Index(fields=['employee_number'], name='hr_hremploy_employe_bbcf81_idx'),
        ),
        migrations.AddIndex(
            model_name='hremployee',
            index=models.Index(fields=['company_id'], name='hr_hremploy_company_266f20_idx'),
        ),
        migrations.AddIndex(
            model_name='hremployee',
            index=models.Index(fields=['department_id'], name='hr_hremploy_departm_3183da_idx'),
        ),
        migrations.AddIndex(
            model_name='hremployee',
            index=models.Index(fields=['job_id'], name='hr_hremploy_job_id__41405b_idx'),
        ),
        migrations.AddIndex(
            model_name='hremployee',
            index=models.Index(fields=['parent_id'], name='hr_hremploy_parent__8f49bf_idx'),
        ),
        migrations.AddIndex(
            model_name='hremployee',
            index=models.Index(fields=['user_id'], name='hr_hremploy_user_id_0e0a28_idx'),
        ),
        migrations.AddConstraint(
            model_name='hremployee',
            constraint=models.UniqueConstraint(condition=models.Q(('employee_number__isnull', False), models.Q(('employee_number', ''), _negated=True)), fields=('employee_number', 'company_id'), name='hr_employee_number_company_uniq'),
        ),
        migrations.AddIndex(
            model_name='hrdepartment',
            index=models.Index(fields=['name'], name='hr_hrdepart_name_d7576c_idx'),
        ),
        migrations.AddIndex(
            model_name='hrdepartment',
            index=models.Index(fields=['company_id'], name='hr_hrdepart_company_a7e078_idx'),
        ),
        migrations.AddIndex(
            model_name='hrdepartment',
            index=models.Index(fields=['parent_id'], name='hr_hrdepart_parent__24c68f_idx'),
        ),
        migrations.AddConstraint(
            model_name='hrdepartment',
            constraint=models.UniqueConstraint(fields=('name', 'company_id'), name='hr_department_name_company_uniq'),
        ),
        migrations.AddIndex(
            model_name='hrcontract',
            index=models.Index(fields=['employee_id'], name='hr_hrcontra_employe_34d102_idx'),
        ),
        migrations.AddIndex(
            model_name='hrcontract',
            index=models.Index(fields=['company_id'], name='hr_hrcontra_company_36282c_idx'),
        ),
        migrations.AddIndex(
            model_name='hrcontract',
            index=models.Index(fields=['state'], name='hr_hrcontra_state_66db8b_idx'),
        ),
        migrations.AddIndex(
            model_name='hrcontract',
            index=models.Index(fields=['date_start'], name='hr_hrcontra_date_st_73e9ed_idx'),
        ),
        migrations.AddIndex(
            model_name='hrcontract',
            index=models.Index(fields=['date_end'], name='hr_hrcontra_date_en_bf7c51_idx'),
        ),
        migrations.AddConstraint(
            model_name='hrcontract',
            constraint=models.CheckConstraint(check=models.Q(('wage__gte', 0)), name='hr_contract_positive_wage'),
        ),
        migrations.AddConstraint(
            model_name='hrcontract',
            constraint=models.CheckConstraint(check=models.Q(('date_end__isnull', True), ('date_end__gte', models.F('date_start')), _connector='OR'), name='hr_contract_valid_dates'),
        ),
        migrations.AddIndex(
            model_name='hrattendance',
            index=models.Index(fields=['employee_id'], name='hr_hrattend_employe_0f613b_idx'),
        ),
        migrations.AddIndex(
            model_name='hrattendance',
            index=models.Index(fields=['check_in'], name='hr_hrattend_check_i_2e3ea3_idx'),
        ),
        migrations.AddIndex(
            model_name='hrattendance',
            index=models.Index(fields=['check_out'], name='hr_hrattend_check_o_6ab48c_idx'),
        ),
        migrations.AddConstraint(
            model_name='hrattendance',
            constraint=models.CheckConstraint(check=models.Q(('check_out__isnull', True), ('check_out__gt', models.F('check_in')), _connector='OR'), name='hr_attendance_valid_times'),
        ),
    ]
