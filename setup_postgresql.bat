@echo off
echo ========================================
echo Django ERP PostgreSQL Setup
echo ========================================

echo.
echo Step 1: Starting PostgreSQL service...
echo ----------------------------------------

REM Try different PostgreSQL service names
net start postgresql-x64-14 2>nul
if %errorlevel% == 0 (
    echo ✅ PostgreSQL service started successfully
    goto :database_setup
)

net start postgresql-x64-13 2>nul
if %errorlevel% == 0 (
    echo ✅ PostgreSQL service started successfully
    goto :database_setup
)

net start postgresql-x64-12 2>nul
if %errorlevel% == 0 (
    echo ✅ PostgreSQL service started successfully
    goto :database_setup
)

net start PostgreSQL 2>nul
if %errorlevel% == 0 (
    echo ✅ PostgreSQL service started successfully
    goto :database_setup
)

echo ❌ Could not start PostgreSQL service automatically
echo.
echo Please start PostgreSQL service manually:
echo 1. Press Win+R, type "services.msc" and press Enter
echo 2. Find PostgreSQL service in the list
echo 3. Right-click and select "Start"
echo 4. Come back and press any key to continue...
pause
echo.

:database_setup
echo Step 2: Creating database and user...
echo ----------------------------------------

REM Try to create database using psql
echo Creating database django_erp and user ERPUser...

REM First try with default postgres user
psql -U postgres -h localhost -f setup_database.sql 2>nul
if %errorlevel% == 0 (
    echo ✅ Database and user created successfully
    goto :django_setup
)

echo.
echo ❌ Could not connect to PostgreSQL with default settings
echo.
echo Please run the following command manually:
echo psql -U postgres -h localhost -f setup_database.sql
echo.
echo If you get authentication errors, you may need to:
echo 1. Set a password for postgres user
echo 2. Update pg_hba.conf to allow local connections
echo.
echo Press any key to continue with Django setup anyway...
pause

:django_setup
echo.
echo Step 3: Setting up Django...
echo ----------------------------------------

echo Installing psycopg2...
pip install psycopg2-binary

echo.
echo Running Django migrations...
python manage.py makemigrations
if %errorlevel% neq 0 (
    echo ❌ makemigrations failed
    goto :error
)

python manage.py migrate
if %errorlevel% neq 0 (
    echo ❌ migrate failed
    goto :error
)

echo.
echo Testing database connection...
python manage.py shell -c "from django.db import connection; cursor = connection.cursor(); cursor.execute('SELECT version()'); print('✅ Database connection successful'); print('PostgreSQL version:', cursor.fetchone()[0])"

if %errorlevel% neq 0 (
    echo ❌ Database connection test failed
    goto :error
)

echo.
echo ========================================
echo ✅ Setup completed successfully!
echo ========================================
echo.
echo Database Details:
echo - Name: django_erp
echo - User: ERPUser
echo - Password: ERPUser
echo - Host: localhost
echo - Port: 5432
echo.
echo Next steps:
echo 1. Create superuser: python manage.py createsuperuser
echo 2. Run server: python manage.py runserver
echo 3. Access admin: http://localhost:8000/admin/
echo.
goto :end

:error
echo.
echo ❌ Setup failed. Please check the errors above.
echo.
echo Manual setup instructions:
echo 1. Ensure PostgreSQL is running
echo 2. Run: psql -U postgres -h localhost -f setup_database.sql
echo 3. Run: python manage.py migrate
echo.

:end
pause
