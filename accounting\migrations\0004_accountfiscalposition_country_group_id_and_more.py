# Generated by Django 4.2.21 on 2025-07-20 09:24

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0004_rename_state_countrystate_countrygroup'),
        ('accounting', '0003_accountpaymentterm_accountfiscalposition'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='accountfiscalposition',
            name='country_group_id',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='core.countrygroup'),
        ),
        migrations.AddField(
            model_name='accountfiscalposition',
            name='country_id',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='core.country'),
        ),
        migrations.AddField(
            model_name='accountfiscalposition',
            name='create_uid',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='accountfiscalposition',
            name='state_ids',
            field=models.ManyToManyField(blank=True, to='core.countrystate'),
        ),
        migrations.AddField(
            model_name='accountfiscalposition',
            name='write_uid',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddIndex(
            model_name='accountpaymentterm',
            index=models.Index(fields=['company_id'], name='accounting__company_812ef3_idx'),
        ),
        migrations.AddIndex(
            model_name='accountfiscalposition',
            index=models.Index(fields=['company_id'], name='accounting__company_267f32_idx'),
        ),
        migrations.AddIndex(
            model_name='accountfiscalposition',
            index=models.Index(fields=['country_id'], name='accounting__country_2f295d_idx'),
        ),
    ]
