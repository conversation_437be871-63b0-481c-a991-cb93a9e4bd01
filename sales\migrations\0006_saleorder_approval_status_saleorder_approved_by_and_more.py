# Generated by Django 4.2.21 on 2025-07-21 03:06

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('project', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('sales', '0005_remove_accountfiscalposition_company_id_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='saleorder',
            name='approval_status',
            field=models.CharField(choices=[('draft', 'Draft'), ('pending', 'Pending Approval'), ('approved', 'Approved'), ('rejected', 'Rejected')], default='draft', help_text='Approval Status', max_length=20),
        ),
        migrations.AddField(
            model_name='saleorder',
            name='approved_by',
            field=models.ForeignKey(blank=True, help_text='Approved By', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_orders', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='saleorder',
            name='approved_date',
            field=models.DateTimeField(blank=True, help_text='Approval Date', null=True),
        ),
        migrations.AddField(
            model_name='saleorder',
            name='credit_limit_check',
            field=models.BooleanField(default=False, help_text='Check Credit Limit'),
        ),
        migrations.AddField(
            model_name='saleorder',
            name='credit_limit_exceeded',
            field=models.BooleanField(default=False, help_text='Credit Limit Exceeded'),
        ),
        migrations.AddField(
            model_name='saleorder',
            name='invoice_policy',
            field=models.CharField(choices=[('order', 'Ordered quantities'), ('delivery', 'Delivered quantities')], default='order', help_text='Invoicing Policy', max_length=20),
        ),
        migrations.AddField(
            model_name='saleorder',
            name='is_subscription',
            field=models.BooleanField(default=False, help_text='Is Subscription Order'),
        ),
        migrations.AddField(
            model_name='saleorder',
            name='milestone_billing',
            field=models.BooleanField(default=False, help_text='Milestone-based billing'),
        ),
        migrations.AddField(
            model_name='saleorder',
            name='next_invoice_date',
            field=models.DateField(blank=True, help_text='Next Invoice Date', null=True),
        ),
        migrations.AddField(
            model_name='saleorder',
            name='recurring_interval',
            field=models.IntegerField(default=1, help_text='Repeat every X periods'),
        ),
        migrations.AddField(
            model_name='saleorder',
            name='recurring_rule_type',
            field=models.CharField(blank=True, choices=[('daily', 'Daily'), ('weekly', 'Weekly'), ('monthly', 'Monthly'), ('yearly', 'Yearly')], help_text='Recurring Rule', max_length=20),
        ),
        migrations.AddField(
            model_name='saleorder',
            name='subscription_management',
            field=models.CharField(blank=True, choices=[('create', 'Create a new subscription'), ('renew', 'Renew existing subscription'), ('upsell', 'Upsell existing subscription')], help_text='Subscription Management', max_length=20),
        ),
        migrations.AddField(
            model_name='saleorderline',
            name='is_milestone',
            field=models.BooleanField(default=False, help_text='Is Milestone Line'),
        ),
        migrations.AddField(
            model_name='saleorderline',
            name='is_service',
            field=models.BooleanField(default=False, help_text='Is Service Line'),
        ),
        migrations.AddField(
            model_name='saleorderline',
            name='is_subscription_line',
            field=models.BooleanField(default=False, help_text='Is Subscription Line'),
        ),
        migrations.AddField(
            model_name='saleorderline',
            name='milestone_date',
            field=models.DateField(blank=True, help_text='Milestone Due Date', null=True),
        ),
        migrations.AddField(
            model_name='saleorderline',
            name='milestone_name',
            field=models.CharField(blank=True, help_text='Milestone Name', max_length=255),
        ),
        migrations.AddField(
            model_name='saleorderline',
            name='milestone_percentage',
            field=models.DecimalField(decimal_places=2, default=0.0, help_text='Milestone Percentage', max_digits=5),
        ),
        migrations.AddField(
            model_name='saleorderline',
            name='milestone_reached',
            field=models.BooleanField(default=False, help_text='Milestone Reached'),
        ),
        migrations.AddField(
            model_name='saleorderline',
            name='subscription_end_date',
            field=models.DateField(blank=True, help_text='Subscription End', null=True),
        ),
        migrations.AddField(
            model_name='saleorderline',
            name='subscription_start_date',
            field=models.DateField(blank=True, help_text='Subscription Start', null=True),
        ),
        migrations.AddField(
            model_name='saleorderline',
            name='timesheet_ids',
            field=models.ManyToManyField(blank=True, help_text='Related Timesheets', to='project.accountanalyticline'),
        ),
    ]
