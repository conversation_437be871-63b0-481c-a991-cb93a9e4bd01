# Generated by Django 4.2.21 on 2025-07-20 09:58

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('accounting', '0004_accountfiscalposition_country_group_id_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='AccountIncoterms',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(help_text='Incoterms Name', max_length=255)),
                ('code', models.CharField(help_text='Incoterms Code', max_length=10, unique=True)),
                ('active', models.<PERSON><PERSON>an<PERSON>ield(default=True)),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'indexes': [models.Index(fields=['code'], name='accounting__code_ac75b8_idx')],
            },
        ),
    ]
