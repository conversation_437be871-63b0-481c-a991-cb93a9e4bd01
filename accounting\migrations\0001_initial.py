# Generated by Django 4.2.21 on 2025-07-19 15:39

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('core', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='AccountAccount',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(db_index=True, max_length=255)),
                ('code', models.CharField(db_index=True, max_length=64)),
                ('account_type', models.CharField(choices=[('asset_receivable', 'Receivable'), ('asset_cash', 'Bank and Cash'), ('asset_current', 'Current Assets'), ('asset_non_current', 'Non-current Assets'), ('asset_prepayments', 'Prepayments'), ('asset_fixed', 'Fixed Assets'), ('liability_payable', 'Payable'), ('liability_credit_card', 'Credit Card'), ('liability_current', 'Current Liabilities'), ('liability_non_current', 'Non-current Liabilities'), ('equity', 'Equity'), ('equity_unaffected', 'Current Year Earnings'), ('income', 'Income'), ('income_other', 'Other Income'), ('expense', 'Expenses'), ('expense_depreciation', 'Depreciation'), ('expense_direct_cost', 'Cost of Revenue'), ('off_balance', 'Off-Balance Sheet')], max_length=50)),
                ('reconcile', models.BooleanField(default=False, help_text='Allow reconciliation of journal items')),
                ('deprecated', models.BooleanField(default=False)),
                ('note', models.TextField(blank=True, help_text='Internal notes')),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('currency_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='core.currency')),
            ],
        ),
        migrations.CreateModel(
            name='AccountFullReconcile',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(max_length=255)),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='AccountJournal',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(max_length=255)),
                ('code', models.CharField(max_length=10)),
                ('type', models.CharField(choices=[('sale', 'Sales'), ('purchase', 'Purchase'), ('cash', 'Cash'), ('bank', 'Bank'), ('general', 'Miscellaneous')], max_length=20)),
                ('sequence', models.IntegerField(default=10, help_text='Used to order journals')),
                ('restrict_mode_hash_table', models.BooleanField(default=True, help_text='Lock posted entries with hash')),
                ('account_control_ids', models.ManyToManyField(blank=True, help_text='Allowed accounts for this journal', to='accounting.accountaccount')),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('currency_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='core.currency')),
                ('default_account_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='journal_default_account', to='accounting.accountaccount')),
                ('suspense_account_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='journal_suspense_account', to='accounting.accountaccount')),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='AccountMove',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(blank=True, help_text='Journal Entry Number', max_length=255)),
                ('ref', models.CharField(blank=True, help_text='Reference', max_length=255)),
                ('date', models.DateField(default=django.utils.timezone.now)),
                ('move_type', models.CharField(choices=[('entry', 'Journal Entry'), ('out_invoice', 'Customer Invoice'), ('out_refund', 'Customer Credit Note'), ('in_invoice', 'Vendor Bill'), ('in_refund', 'Vendor Credit Note'), ('out_receipt', 'Sales Receipt'), ('in_receipt', 'Purchase Receipt')], default='entry', max_length=20)),
                ('state', models.CharField(choices=[('draft', 'Unposted'), ('posted', 'Posted'), ('cancel', 'Cancelled')], default='draft', max_length=20)),
                ('invoice_date', models.DateField(blank=True, null=True)),
                ('invoice_date_due', models.DateField(blank=True, null=True)),
                ('payment_reference', models.CharField(blank=True, max_length=255)),
                ('payment_state', models.CharField(choices=[('not_paid', 'Not Paid'), ('in_payment', 'In Payment'), ('paid', 'Paid'), ('partial', 'Partially Paid'), ('reversed', 'Reversed'), ('invoicing_legacy', 'Invoicing App Legacy')], default='not_paid', max_length=20)),
                ('amount_untaxed', models.DecimalField(decimal_places=2, default=0.0, max_digits=20)),
                ('amount_tax', models.DecimalField(decimal_places=2, default=0.0, max_digits=20)),
                ('amount_total', models.DecimalField(decimal_places=2, default=0.0, max_digits=20)),
                ('amount_residual', models.DecimalField(decimal_places=2, default=0.0, max_digits=20)),
                ('posted_before', models.BooleanField(default=False)),
                ('to_check', models.BooleanField(default=False)),
                ('inalterable_hash', models.CharField(blank=True, max_length=255)),
                ('secure_sequence_number', models.IntegerField(blank=True, null=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('currency_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.currency')),
                ('journal_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='accounting.accountjournal')),
                ('partner_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='core.partner')),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='AccountMoveLine',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('debit', models.DecimalField(decimal_places=2, default=0.0, max_digits=20)),
                ('credit', models.DecimalField(decimal_places=2, default=0.0, max_digits=20)),
                ('balance', models.DecimalField(decimal_places=2, default=0.0, max_digits=20)),
                ('amount_currency', models.DecimalField(decimal_places=2, default=0.0, max_digits=20)),
                ('name', models.CharField(help_text='Label', max_length=255)),
                ('ref', models.CharField(blank=True, help_text='Reference', max_length=255)),
                ('date', models.DateField()),
                ('date_maturity', models.DateField(blank=True, help_text='Due date', null=True)),
                ('tax_base_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=20)),
                ('reconciled', models.BooleanField(default=False)),
                ('matching_number', models.CharField(blank=True, max_length=255)),
                ('quantity', models.DecimalField(decimal_places=4, default=1.0, max_digits=16)),
                ('price_unit', models.DecimalField(decimal_places=4, default=0.0, max_digits=20)),
                ('discount', models.DecimalField(decimal_places=2, default=0.0, max_digits=16)),
                ('sequence', models.IntegerField(default=10)),
                ('display_type', models.CharField(blank=True, choices=[('line_section', 'Section'), ('line_note', 'Note')], max_length=20, null=True)),
                ('account_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='accounting.accountaccount')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('currency_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='core.currency')),
                ('full_reconcile_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.accountfullreconcile')),
                ('move_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='line_ids', to='accounting.accountmove')),
                ('partner_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='core.partner')),
            ],
        ),
        migrations.CreateModel(
            name='AccountTaxRepartitionLine',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('factor_percent', models.DecimalField(decimal_places=4, default=100.0, max_digits=16)),
                ('repartition_type', models.CharField(choices=[('base', 'Base'), ('tax', 'Tax')], default='tax', max_length=10)),
                ('sequence', models.IntegerField(default=1)),
                ('account_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='accounting.accountaccount')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='AccountTax',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(max_length=255)),
                ('type_tax_use', models.CharField(choices=[('sale', 'Sales'), ('purchase', 'Purchase'), ('none', 'None')], default='sale', max_length=20)),
                ('tax_scope', models.CharField(choices=[('service', 'Services'), ('consu', 'Goods')], default='consu', max_length=20)),
                ('amount_type', models.CharField(choices=[('group', 'Group of Taxes'), ('fixed', 'Fixed'), ('percent', 'Percentage of Price'), ('division', 'Percentage of Price Tax Included')], default='percent', max_length=20)),
                ('amount', models.DecimalField(decimal_places=4, default=0.0, max_digits=16)),
                ('sequence', models.IntegerField(default=1)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('invoice_repartition_line_ids', models.ManyToManyField(blank=True, related_name='invoice_tax_ids', to='accounting.accounttaxrepartitionline')),
                ('refund_repartition_line_ids', models.ManyToManyField(blank=True, related_name='refund_tax_ids', to='accounting.accounttaxrepartitionline')),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'unique_together': {('name', 'type_tax_use', 'tax_scope', 'company_id')},
            },
        ),
        migrations.CreateModel(
            name='AccountPartialReconcile',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=20)),
                ('amount_currency', models.DecimalField(decimal_places=2, default=0.0, max_digits=20)),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('credit_move_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='matched_credit_ids', to='accounting.accountmoveline')),
                ('currency_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='core.currency')),
                ('debit_move_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='matched_debit_ids', to='accounting.accountmoveline')),
                ('full_reconcile_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='accounting.accountfullreconcile')),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.AddField(
            model_name='accountmoveline',
            name='tax_ids',
            field=models.ManyToManyField(blank=True, related_name='move_line_tax_ids', to='accounting.accounttax'),
        ),
        migrations.AddField(
            model_name='accountmoveline',
            name='tax_line_id',
            field=models.ForeignKey(blank=True, help_text='Tax this line is part of', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='tax_move_line_ids', to='accounting.accounttax'),
        ),
        migrations.AddField(
            model_name='accountmoveline',
            name='write_uid',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL),
        ),
        migrations.CreateModel(
            name='AccountGroup',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(max_length=255)),
                ('code_prefix_start', models.CharField(blank=True, max_length=20)),
                ('code_prefix_end', models.CharField(blank=True, max_length=20)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('parent_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='accounting.accountgroup')),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.AddField(
            model_name='accountfullreconcile',
            name='reconciled_line_ids',
            field=models.ManyToManyField(related_name='full_reconcile_ids', to='accounting.accountmoveline'),
        ),
        migrations.AddField(
            model_name='accountfullreconcile',
            name='write_uid',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='accountaccount',
            name='group_id',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.accountgroup'),
        ),
        migrations.AddField(
            model_name='accountaccount',
            name='tax_ids',
            field=models.ManyToManyField(blank=True, help_text='Default taxes for this account', to='accounting.accounttax'),
        ),
        migrations.AddField(
            model_name='accountaccount',
            name='write_uid',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddIndex(
            model_name='accountmoveline',
            index=models.Index(fields=['move_id'], name='accounting__move_id_95d433_idx'),
        ),
        migrations.AddIndex(
            model_name='accountmoveline',
            index=models.Index(fields=['account_id'], name='accounting__account_af77b2_idx'),
        ),
        migrations.AddIndex(
            model_name='accountmoveline',
            index=models.Index(fields=['partner_id'], name='accounting__partner_492bcf_idx'),
        ),
        migrations.AddIndex(
            model_name='accountmoveline',
            index=models.Index(fields=['date'], name='accounting__date_927817_idx'),
        ),
        migrations.AddIndex(
            model_name='accountmoveline',
            index=models.Index(fields=['reconciled'], name='accounting__reconci_bf53cc_idx'),
        ),
        migrations.AddConstraint(
            model_name='accountmoveline',
            constraint=models.CheckConstraint(check=models.Q(('debit__gte', 0)), name='check_debit_positive'),
        ),
        migrations.AddConstraint(
            model_name='accountmoveline',
            constraint=models.CheckConstraint(check=models.Q(('credit__gte', 0)), name='check_credit_positive'),
        ),
        migrations.AddIndex(
            model_name='accountmove',
            index=models.Index(fields=['date', 'name'], name='accounting__date_1f0f45_idx'),
        ),
        migrations.AddIndex(
            model_name='accountmove',
            index=models.Index(fields=['state'], name='accounting__state_dd2545_idx'),
        ),
        migrations.AddIndex(
            model_name='accountmove',
            index=models.Index(fields=['move_type'], name='accounting__move_ty_44114b_idx'),
        ),
        migrations.AddIndex(
            model_name='accountmove',
            index=models.Index(fields=['partner_id'], name='accounting__partner_3ded69_idx'),
        ),
        migrations.AddIndex(
            model_name='accountmove',
            index=models.Index(fields=['journal_id'], name='accounting__journal_565da1_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='accountjournal',
            unique_together={('code', 'company_id')},
        ),
        migrations.AddIndex(
            model_name='accountaccount',
            index=models.Index(fields=['code', 'company_id'], name='accounting__code_583b94_idx'),
        ),
        migrations.AddIndex(
            model_name='accountaccount',
            index=models.Index(fields=['account_type'], name='accounting__account_12f259_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='accountaccount',
            unique_together={('code', 'company_id')},
        ),
    ]
