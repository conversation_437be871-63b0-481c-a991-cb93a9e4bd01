from django.test import TestCase
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from decimal import Decimal
from datetime import timedelta, date
from django.utils import timezone
from core.models import Company, Country, Currency, Partner
from .models import (
    ProjectProject, ProjectTags, ProjectTaskType, ProjectTask,
    ProjectMilestone, AccountAnalyticLine, AccountAnalyticTag
)


class ProjectModelsTestCase(TestCase):
    def setUp(self):
        """Set up test data"""
        # Create users
        self.user = User.objects.create_user(
            username='projectuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.project_manager = User.objects.create_user(
            username='pm',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.developer = User.objects.create_user(
            username='developer',
            email='<EMAIL>',
            password='testpass123'
        )

        # Create country and currency
        self.country = Country.objects.create(
            name='United States',
            code='US',
            create_uid=self.user,
            write_uid=self.user
        )

        self.currency = Currency.objects.create(
            name='USD',
            symbol='$',
            create_uid=self.user,
            write_uid=self.user
        )

        # Create company
        self.company = Company.objects.create(
            name='Test Project Company',
            code='TPC',
            currency_id=self.currency,
            country_id=self.country,
            create_uid=self.user,
            write_uid=self.user
        )

        # Create customer
        self.customer = Partner.objects.create(
            name='Test Customer',
            customer_rank=1,
            email='<EMAIL>',
            create_uid=self.user,
            write_uid=self.user
        )

        # Create project tags
        self.tag_urgent = ProjectTags.objects.create(
            name='Urgent',
            color=1,
            create_uid=self.user,
            write_uid=self.user
        )

        # Create project
        self.project = ProjectProject.objects.create(
            name='Test Project',
            user_id=self.project_manager,
            partner_id=self.customer,
            company_id=self.company,
            allow_timesheets=True,
            allow_billable_hours=True,
            date_start=date.today(),
            date=date.today() + timedelta(days=30),
            create_uid=self.user,
            write_uid=self.user
        )

        # Create task stages
        self.stage_todo = ProjectTaskType.objects.create(
            name='To Do',
            sequence=1,
            create_uid=self.user,
            write_uid=self.user
        )
        
        self.stage_progress = ProjectTaskType.objects.create(
            name='In Progress',
            sequence=2,
            create_uid=self.user,
            write_uid=self.user
        )
        
        self.stage_done = ProjectTaskType.objects.create(
            name='Done',
            sequence=3,
            fold=True,
            create_uid=self.user,
            write_uid=self.user
        )

        # Create milestone
        self.milestone = ProjectMilestone.objects.create(
            name='Phase 1 Complete',
            project_id=self.project,
            deadline=date.today() + timedelta(days=15),
            create_uid=self.user,
            write_uid=self.user
        )

        # Create analytic tag
        self.analytic_tag = AccountAnalyticTag.objects.create(
            name='Development',
            color=2,
            create_uid=self.user,
            write_uid=self.user
        )

    def test_project_creation_and_validation(self):
        """Test project creation and validation"""
        project = ProjectProject.objects.create(
            name='New Project',
            user_id=self.project_manager,
            company_id=self.company,
            allow_timesheets=True,
            privacy_visibility='employees',
            create_uid=self.user,
            write_uid=self.user
        )
        
        self.assertEqual(project.name, 'New Project')
        self.assertEqual(project.user_id, self.project_manager)
        self.assertTrue(project.allow_timesheets)
        self.assertEqual(project.privacy_visibility, 'employees')
        self.assertTrue(project.active)

    def test_project_date_validation(self):
        """Test project date validation"""
        # Test invalid dates (end before start)
        with self.assertRaises(ValidationError):
            project = ProjectProject(
                name='Invalid Project',
                company_id=self.company,
                date_start=date.today(),
                date=date.today() - timedelta(days=1),  # End before start
                create_uid=self.user,
                write_uid=self.user
            )
            project.full_clean()

    def test_project_close_open_actions(self):
        """Test project close/open actions"""
        # Test closing project
        self.assertTrue(self.project.active)
        self.project.action_close()
        self.assertFalse(self.project.active)
        self.assertTrue(self.project.is_closed)
        
        # Test opening project
        self.project.action_open()
        self.assertTrue(self.project.active)
        self.assertFalse(self.project.is_closed)

    def test_task_creation_and_validation(self):
        """Test task creation and validation"""
        task = ProjectTask.objects.create(
            name='Test Task',
            project_id=self.project,
            stage_id=self.stage_todo,
            priority='1',  # High priority
            planned_hours=8.0,
            description='Test task description',
            date_deadline=date.today() + timedelta(days=7),
            milestone_id=self.milestone,
            company_id=self.company,  # Required field
            create_uid=self.user,
            write_uid=self.user
        )
        
        self.assertEqual(task.name, 'Test Task')
        self.assertEqual(task.project_id, self.project)
        self.assertEqual(task.stage_id, self.stage_todo)
        self.assertEqual(task.priority, '1')
        self.assertEqual(task.planned_hours, 8.0)
        self.assertEqual(task.company_id, self.project.company_id)  # Auto-set from project
        self.assertEqual(task.partner_id, self.project.partner_id)  # Auto-set from project

    def test_task_assignment_and_users(self):
        """Test task assignment to multiple users"""
        task = ProjectTask.objects.create(
            name='Multi-user Task',
            project_id=self.project,
            stage_id=self.stage_progress,
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )
        
        # Assign multiple users
        task.user_ids.add(self.developer, self.project_manager)
        
        self.assertEqual(task.user_ids.count(), 2)
        self.assertIn(self.developer, task.user_ids.all())
        self.assertIn(self.project_manager, task.user_ids.all())

    def test_task_hierarchy_validation(self):
        """Test task hierarchy validation"""
        parent_task = ProjectTask.objects.create(
            name='Parent Task',
            project_id=self.project,
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )

        child_task = ProjectTask.objects.create(
            name='Child Task',
            project_id=self.project,
            parent_id=parent_task,
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )
        
        self.assertEqual(child_task.parent_id, parent_task)
        
        # Test self-parent validation
        with self.assertRaises(ValidationError):
            parent_task.parent_id = parent_task
            parent_task.full_clean()

    def test_task_progress_calculation(self):
        """Test task progress calculation based on hours"""
        task = ProjectTask.objects.create(
            name='Progress Task',
            project_id=self.project,
            planned_hours=10.0,
            effective_hours=3.0,
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )
        
        # Progress should be calculated automatically
        self.assertEqual(task.progress, 30.0)  # 3/10 * 100
        self.assertEqual(task.remaining_hours, 7.0)  # 10 - 3

    def test_task_close_open_actions(self):
        """Test task close/open actions"""
        task = ProjectTask.objects.create(
            name='Action Task',
            project_id=self.project,
            stage_id=self.stage_progress,
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )
        
        # Test closing task
        task.action_close()
        self.assertEqual(task.kanban_state, 'done')
        self.assertEqual(task.progress, 100.0)
        
        # Test reopening task
        task.action_open()
        self.assertEqual(task.kanban_state, 'normal')
        self.assertEqual(task.progress, 0.0)

    def test_milestone_creation_and_actions(self):
        """Test milestone creation and reach/unreach actions"""
        milestone = ProjectMilestone.objects.create(
            name='Test Milestone',
            project_id=self.project,
            deadline=date.today() + timedelta(days=10),
            description='Test milestone description',
            create_uid=self.user,
            write_uid=self.user
        )
        
        self.assertEqual(milestone.name, 'Test Milestone')
        self.assertEqual(milestone.project_id, self.project)
        self.assertFalse(milestone.is_reached)
        
        # Test reaching milestone
        milestone.action_reach()
        self.assertTrue(milestone.is_reached)
        
        # Test unreaching milestone
        milestone.action_unreach()
        self.assertFalse(milestone.is_reached)

    def test_timesheet_creation_and_validation(self):
        """Test timesheet (analytic line) creation and validation"""
        task = ProjectTask.objects.create(
            name='Timesheet Task',
            project_id=self.project,
            planned_hours=8.0,
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )
        
        timesheet = AccountAnalyticLine.objects.create(
            name='Development work',
            date=date.today(),
            project_id=self.project,
            task_id=task,
            user_id=self.developer,
            unit_amount=4.0,  # 4 hours
            amount=Decimal('200.00'),  # $50/hour
            company_id=self.company,  # Required field
            create_uid=self.user,
            write_uid=self.user
        )
        
        self.assertEqual(timesheet.name, 'Development work')
        self.assertEqual(timesheet.project_id, self.project)
        self.assertEqual(timesheet.task_id, task)
        self.assertEqual(timesheet.unit_amount, 4.0)
        self.assertEqual(timesheet.company_id, self.project.company_id)  # Auto-set
        self.assertEqual(timesheet.currency_id, self.company.currency_id)  # Auto-set

    def test_timesheet_negative_hours_validation(self):
        """Test timesheet negative hours validation"""
        with self.assertRaises(ValidationError):
            timesheet = AccountAnalyticLine(
                name='Invalid timesheet',
                project_id=self.project,
                user_id=self.developer,
                unit_amount=-2.0,  # Negative hours
                create_uid=self.user,
                write_uid=self.user
            )
            timesheet.full_clean()

    def test_timesheet_task_hours_update(self):
        """Test that timesheet updates task effective hours"""
        task = ProjectTask.objects.create(
            name='Hours Update Task',
            project_id=self.project,
            planned_hours=10.0,
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )
        
        # Create first timesheet
        AccountAnalyticLine.objects.create(
            name='Work 1',
            project_id=self.project,
            task_id=task,
            user_id=self.developer,
            unit_amount=3.0,
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )
        
        # Refresh task from database
        task.refresh_from_db()
        self.assertEqual(task.effective_hours, 3.0)
        
        # Create second timesheet
        AccountAnalyticLine.objects.create(
            name='Work 2',
            project_id=self.project,
            task_id=task,
            user_id=self.developer,
            unit_amount=2.0,
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )
        
        # Refresh task from database
        task.refresh_from_db()
        self.assertEqual(task.effective_hours, 5.0)  # 3 + 2

    def test_project_task_count_update(self):
        """Test project task count update"""
        initial_count = self.project.task_count
        
        # Create new task
        task = ProjectTask.objects.create(
            name='Count Task',
            project_id=self.project,
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )
        
        # Add task to project's task_ids
        self.project.task_ids.add(task)
        self.project.save()
        
        # Task count should be updated
        self.assertEqual(self.project.task_count, initial_count + 1)

    def test_analytic_tag_creation(self):
        """Test analytic tag creation"""
        tag = AccountAnalyticTag.objects.create(
            name='Testing',
            color=3,
            active=True,
            create_uid=self.user,
            write_uid=self.user
        )
        
        self.assertEqual(tag.name, 'Testing')
        self.assertEqual(tag.color, 3)
        self.assertTrue(tag.active)

    def test_project_tags_integration(self):
        """Test project tags integration"""
        # Add tag to project
        self.project.tag_ids.add(self.tag_urgent)
        
        self.assertEqual(self.project.tag_ids.count(), 1)
        self.assertIn(self.tag_urgent, self.project.tag_ids.all())

    def test_task_tags_integration(self):
        """Test task tags integration"""
        task = ProjectTask.objects.create(
            name='Tagged Task',
            project_id=self.project,
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )
        
        # Add tag to task
        task.tag_ids.add(self.tag_urgent)
        
        self.assertEqual(task.tag_ids.count(), 1)
        self.assertIn(self.tag_urgent, task.tag_ids.all())
