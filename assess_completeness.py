#!/usr/bin/env python
"""
Django ERP Completeness Assessment
Analyzes what's implemented vs what's missing
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'django_erp.settings')
django.setup()

from django.db import connection
from django.apps import apps

def assess_database_level():
    """Assess database level completeness"""
    print("🗄️  DATABASE LEVEL ASSESSMENT")
    print("=" * 50)
    
    assessments = []
    
    # Check table structure
    with connection.cursor() as cursor:
        cursor.execute("""
            SELECT COUNT(*) FROM information_schema.tables 
            WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
        """)
        table_count = cursor.fetchone()[0]
    
    assessments.append(("Table Structure", "✅ COMPLETE", f"{table_count} tables created"))
    
    # Check master data
    try:
        from core.models import Currency, Country, Company
        from accounting.models import AccountAccount, AccountJournal
        
        currency_count = Currency.objects.count()
        country_count = Country.objects.count()
        company_count = Company.objects.count()
        account_count = AccountAccount.objects.count()
        journal_count = AccountJournal.objects.count()
        
        if currency_count == 0:
            assessments.append(("Master Data - Currencies", "❌ MISSING", "No currencies loaded"))
        else:
            assessments.append(("Master Data - Currencies", "✅ COMPLETE", f"{currency_count} currencies"))
            
        if country_count == 0:
            assessments.append(("Master Data - Countries", "❌ MISSING", "No countries loaded"))
        else:
            assessments.append(("Master Data - Countries", "✅ COMPLETE", f"{country_count} countries"))
            
        if account_count == 0:
            assessments.append(("Chart of Accounts", "❌ MISSING", "No accounts configured"))
        else:
            assessments.append(("Chart of Accounts", "✅ COMPLETE", f"{account_count} accounts"))
            
        if journal_count == 0:
            assessments.append(("Accounting Journals", "❌ MISSING", "No journals configured"))
        else:
            assessments.append(("Accounting Journals", "✅ COMPLETE", f"{journal_count} journals"))
            
    except Exception as e:
        assessments.append(("Master Data Check", "❌ ERROR", str(e)))
    
    # Check indexes
    with connection.cursor() as cursor:
        cursor.execute("""
            SELECT COUNT(*) FROM pg_indexes 
            WHERE schemaname = 'public' AND indexname NOT LIKE '%_pkey'
        """)
        index_count = cursor.fetchone()[0]
    
    if index_count < 20:  # Rough estimate for needed indexes
        assessments.append(("Performance Indexes", "⚠️  PARTIAL", f"Only {index_count} custom indexes"))
    else:
        assessments.append(("Performance Indexes", "✅ COMPLETE", f"{index_count} indexes"))
    
    # Print results
    for item, status, detail in assessments:
        print(f"{status} {item:<25} - {detail}")
    
    return assessments

def assess_backend_level():
    """Assess backend level completeness"""
    print("\n🏗️  BACKEND LEVEL ASSESSMENT")
    print("=" * 50)
    
    assessments = []
    
    # Check model methods
    try:
        from accounting.models import AccountMove
        from sales.models import SaleOrder
        from purchases.models import PurchaseOrder
        
        # Check if key business methods exist
        move_methods = [method for method in dir(AccountMove) if not method.startswith('_')]
        order_methods = [method for method in dir(SaleOrder) if not method.startswith('_')]
        po_methods = [method for method in dir(PurchaseOrder) if not method.startswith('_')]
        
        # Key methods that should exist
        required_move_methods = ['action_post', 'button_draft', '_reverse_moves', '_check_balanced']
        required_order_methods = ['action_confirm', 'action_cancel', '_create_invoices']
        required_po_methods = ['button_confirm', 'button_approve', '_create_bills']
        
        move_complete = all(hasattr(AccountMove, method) for method in required_move_methods)
        order_complete = all(hasattr(SaleOrder, method) for method in required_order_methods)
        po_complete = all(hasattr(PurchaseOrder, method) for method in required_po_methods)
        
        if move_complete:
            assessments.append(("Accounting Business Logic", "✅ COMPLETE", "Key methods implemented"))
        else:
            missing = [m for m in required_move_methods if not hasattr(AccountMove, m)]
            assessments.append(("Accounting Business Logic", "⚠️  PARTIAL", f"Missing: {missing}"))

        if order_complete:
            assessments.append(("Sales Business Logic", "✅ COMPLETE", "Key methods implemented"))
        else:
            missing = [m for m in required_order_methods if not hasattr(SaleOrder, m)]
            assessments.append(("Sales Business Logic", "⚠️  PARTIAL", f"Missing: {missing}"))

        if po_complete:
            assessments.append(("Purchase Business Logic", "✅ COMPLETE", "Key methods implemented"))
        else:
            missing = [m for m in required_po_methods if not hasattr(PurchaseOrder, m)]
            assessments.append(("Purchase Business Logic", "⚠️  PARTIAL", f"Missing: {missing}"))
            
    except Exception as e:
        assessments.append(("Business Logic Check", "❌ ERROR", str(e)))
    
    # Check API layer
    try:
        import rest_framework
        assessments.append(("REST API Framework", "✅ INSTALLED", "Django REST Framework available"))
    except ImportError:
        assessments.append(("REST API Framework", "❌ MISSING", "Django REST Framework not installed"))
    
    # Check if API views exist
    api_files = [
        'core/api.py', 'accounting/api.py', 'sales/api.py', 
        'purchases/api.py', 'inventory/api.py'
    ]
    
    api_exists = any(os.path.exists(f) for f in api_files)
    if api_exists:
        assessments.append(("API Endpoints", "⚠️  PARTIAL", "Some API files exist"))
    else:
        assessments.append(("API Endpoints", "❌ MISSING", "No API endpoints implemented"))
    
    # Check testing
    test_files = [
        'core/tests.py', 'accounting/tests.py', 'sales/tests.py',
        'purchases/tests.py', 'inventory/tests.py'
    ]
    
    test_count = sum(1 for f in test_files if os.path.exists(f) and os.path.getsize(f) > 1000)
    if test_count >= 4:
        assessments.append(("Test Coverage", "✅ GOOD", f"{test_count}/5 modules have substantial tests"))
    elif test_count >= 2:
        assessments.append(("Test Coverage", "⚠️  PARTIAL", f"{test_count}/5 modules have tests"))
    else:
        assessments.append(("Test Coverage", "❌ POOR", f"Only {test_count}/5 modules have tests"))
    
    # Check UI beyond admin
    ui_files = ['templates/', 'static/', 'frontend/']
    ui_exists = any(os.path.exists(f) for f in ui_files)
    if ui_exists:
        assessments.append(("User Interface", "⚠️  PARTIAL", "Some UI files exist"))
    else:
        assessments.append(("User Interface", "❌ MISSING", "Only Django admin interface"))
    
    # Print results
    for item, status, detail in assessments:
        print(f"{status} {item:<25} - {detail}")
    
    return assessments

def calculate_completion_percentage(db_assessments, backend_assessments):
    """Calculate overall completion percentage"""
    print("\n📊 COMPLETION ANALYSIS")
    print("=" * 50)
    
    all_assessments = db_assessments + backend_assessments
    
    complete_count = sum(1 for _, status, _ in all_assessments if "✅" in status)
    partial_count = sum(1 for _, status, _ in all_assessments if "⚠️" in status)
    missing_count = sum(1 for _, status, _ in all_assessments if "❌" in status)
    
    total_count = len(all_assessments)
    
    # Weight: Complete = 1.0, Partial = 0.5, Missing = 0.0
    weighted_score = (complete_count * 1.0 + partial_count * 0.5) / total_count * 100
    
    print(f"✅ Complete: {complete_count}/{total_count}")
    print(f"⚠️  Partial:  {partial_count}/{total_count}")
    print(f"❌ Missing:  {missing_count}/{total_count}")
    print(f"\n🎯 Overall Completion: {weighted_score:.1f}%")
    
    return weighted_score

def main():
    """Main assessment function"""
    print("🔍 Django ERP Completeness Assessment")
    print("=" * 60)
    
    # Run assessments
    db_assessments = assess_database_level()
    backend_assessments = assess_backend_level()
    
    # Calculate completion
    completion = calculate_completion_percentage(db_assessments, backend_assessments)
    
    # Recommendations
    print(f"\n💡 RECOMMENDATIONS")
    print("=" * 50)
    
    if completion < 40:
        print("🔴 CRITICAL: Focus on master data and core business logic")
        print("   Priority: Load currencies, countries, chart of accounts")
    elif completion < 70:
        print("🟡 IMPORTANT: Implement missing business workflows")
        print("   Priority: Complete API layer and advanced business logic")
    else:
        print("🟢 GOOD: Focus on optimization and advanced features")
        print("   Priority: Performance tuning and user interface")
    
    print(f"\n🚀 NEXT STEPS:")
    print("1. Run: python manage.py shell")
    print("2. Create master data (currencies, countries, accounts)")
    print("3. Implement missing business methods")
    print("4. Add REST API endpoints")
    print("5. Enhance test coverage")
    
    return completion

if __name__ == "__main__":
    completion = main()
    print(f"\n📈 System is {completion:.1f}% complete")
    sys.exit(0)
