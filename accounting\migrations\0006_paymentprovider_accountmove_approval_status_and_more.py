# Generated by Django 4.2.21 on 2025-07-21 03:15

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('crm', '0001_initial'),
        ('core', '0004_rename_state_countrystate_countrygroup'),
        ('accounting', '0005_accountincoterms'),
    ]

    operations = [
        migrations.CreateModel(
            name='PaymentProvider',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(help_text='Provider Name', max_length=255)),
                ('code', models.Char<PERSON>ield(choices=[('paypal', 'PayPal'), ('stripe', 'Stripe'), ('razorpay', 'Razorpay'), ('manual', 'Manual')], help_text='Provider Code', max_length=50)),
                ('state', models.CharField(choices=[('disabled', 'Disabled'), ('enabled', 'Enabled'), ('test', 'Test Mode')], default='disabled', max_length=20)),
                ('is_published', models.BooleanField(default=False, help_text='Published on Website')),
                ('maximum_amount', models.DecimalField(decimal_places=2, default=0.0, help_text='Maximum Payment Amount', max_digits=20)),
                ('api_key', models.CharField(blank=True, help_text='API Key', max_length=255)),
                ('api_secret', models.CharField(blank=True, help_text='API Secret', max_length=255)),
                ('webhook_secret', models.CharField(blank=True, help_text='Webhook Secret', max_length=255)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.AddField(
            model_name='accountmove',
            name='approval_status',
            field=models.CharField(choices=[('draft', 'Draft'), ('pending', 'Pending Approval'), ('approved', 'Approved'), ('rejected', 'Rejected')], default='draft', help_text='Approval Status', max_length=20),
        ),
        migrations.AddField(
            model_name='accountmove',
            name='approved_by',
            field=models.ForeignKey(blank=True, help_text='Approved By', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_invoices', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='accountmove',
            name='approved_date',
            field=models.DateTimeField(blank=True, help_text='Approval Date', null=True),
        ),
        migrations.AddField(
            model_name='accountmove',
            name='auto_post',
            field=models.BooleanField(default=False, help_text='Auto Post'),
        ),
        migrations.AddField(
            model_name='accountmove',
            name='auto_post_until',
            field=models.DateField(blank=True, help_text='Auto Post Until', null=True),
        ),
        migrations.AddField(
            model_name='accountmove',
            name='fiscal_position_id',
            field=models.ForeignKey(blank=True, help_text='Fiscal Position', null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.accountfiscalposition'),
        ),
        migrations.AddField(
            model_name='accountmove',
            name='invoice_origin',
            field=models.CharField(blank=True, help_text='Source Document', max_length=255),
        ),
        migrations.AddField(
            model_name='accountmove',
            name='invoice_partner_display_name',
            field=models.CharField(blank=True, help_text='Partner Display Name', max_length=255),
        ),
        migrations.AddField(
            model_name='accountmove',
            name='invoice_payment_term_id',
            field=models.ForeignKey(blank=True, help_text='Payment Terms', null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.accountpaymentterm'),
        ),
        migrations.AddField(
            model_name='accountmove',
            name='invoice_sent',
            field=models.BooleanField(default=False, help_text='Invoice Sent'),
        ),
        migrations.AddField(
            model_name='accountmove',
            name='invoice_sent_date',
            field=models.DateTimeField(blank=True, help_text='Invoice Sent Date', null=True),
        ),
        migrations.AddField(
            model_name='accountmove',
            name='invoice_source_email',
            field=models.EmailField(blank=True, help_text='Source Email', max_length=254),
        ),
        migrations.AddField(
            model_name='accountmove',
            name='is_recurring',
            field=models.BooleanField(default=False, help_text='Is Recurring Invoice'),
        ),
        migrations.AddField(
            model_name='accountmove',
            name='partner_shipping_id',
            field=models.ForeignKey(blank=True, help_text='Delivery Address', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='shipping_invoices', to='core.partner'),
        ),
        migrations.AddField(
            model_name='accountmove',
            name='recurring_source_id',
            field=models.ForeignKey(blank=True, help_text='Source Recurring Invoice', null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.accountmove'),
        ),
        migrations.AddField(
            model_name='accountmove',
            name='team_id',
            field=models.ForeignKey(blank=True, help_text='Sales Team', null=True, on_delete=django.db.models.deletion.SET_NULL, to='crm.crmteam'),
        ),
        migrations.AddField(
            model_name='accountmove',
            name='user_id',
            field=models.ForeignKey(blank=True, help_text='Salesperson', null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL),
        ),
        migrations.CreateModel(
            name='PaymentTransaction',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('reference', models.CharField(help_text='Transaction Reference', max_length=255, unique=True)),
                ('provider_reference', models.CharField(blank=True, help_text='Provider Reference', max_length=255)),
                ('amount', models.DecimalField(decimal_places=2, help_text='Transaction Amount', max_digits=20)),
                ('state', models.CharField(choices=[('draft', 'Draft'), ('pending', 'Pending'), ('authorized', 'Authorized'), ('done', 'Done'), ('cancel', 'Cancelled'), ('error', 'Error')], default='draft', max_length=20)),
                ('operation', models.CharField(blank=True, choices=[('online_redirect', 'Online payment with redirection'), ('online_direct', 'Online direct payment'), ('online_token', 'Online payment by token'), ('validation', 'Validation of the payment method'), ('offline', 'Offline payment by token'), ('refund', 'Refund')], max_length=50)),
                ('partner_name', models.CharField(blank=True, max_length=255)),
                ('partner_email', models.EmailField(blank=True, max_length=254)),
                ('partner_phone', models.CharField(blank=True, max_length=50)),
                ('provider_data', models.TextField(blank=True, help_text='Provider-specific data (JSON)')),
                ('last_state_change', models.DateTimeField(auto_now=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('currency_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.currency')),
                ('invoice_ids', models.ManyToManyField(blank=True, help_text='Related Invoices', to='accounting.accountmove')),
                ('partner_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.partner')),
                ('payment_id', models.ForeignKey(blank=True, help_text='Generated Payment', null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.accountpayment')),
                ('provider_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='accounting.paymentprovider')),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.AddField(
            model_name='accountmove',
            name='payment_transaction_ids',
            field=models.ManyToManyField(blank=True, help_text='Payment Transactions', to='accounting.paymenttransaction'),
        ),
    ]
