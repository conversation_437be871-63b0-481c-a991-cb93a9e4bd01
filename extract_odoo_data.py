#!/usr/bin/env python
"""
Extract data from Odoo database for Django ERP
"""

import psycopg2
import json

def extract_currencies():
    """Extract currency data from Odoo"""
    print("🔍 Extracting currencies from Odoo...")
    
    try:
        conn = psycopg2.connect(
            host='localhost',
            port='5432',
            database='erp',
            user='postgres',
            password='postgres'
        )
        cursor = conn.cursor()
        
        # Get currency data
        cursor.execute("""
            SELECT name, symbol, decimal_places, full_name, position, 
                   currency_unit_label, currency_subunit_label, rounding, active
            FROM res_currency 
            WHERE active = true 
            ORDER BY name
        """)
        
        currencies = cursor.fetchall()
        
        print(f"Found {len(currencies)} active currencies:")
        currency_data = []
        
        for curr in currencies:
            currency_info = {
                'name': curr[0],  # ISO code
                'symbol': curr[1],
                'decimal_places': curr[2],
                'full_name': curr[3],
                'position': curr[4],
                'currency_unit_label': curr[5],
                'currency_subunit_label': curr[6],
                'rounding': float(curr[7]) if curr[7] else 0.01,
                'active': curr[8]
            }
            currency_data.append(currency_info)
            print(f"  {curr[0]} - {curr[3]} ({curr[1]})")
        
        conn.close()
        return currency_data
        
    except Exception as e:
        print(f"Error extracting currencies: {e}")
        return []

def extract_countries():
    """Extract country data from Odoo"""
    print("\n🔍 Extracting countries from Odoo...")
    
    try:
        conn = psycopg2.connect(
            host='localhost',
            port='5432',
            database='erp',
            user='postgres',
            password='postgres'
        )
        cursor = conn.cursor()
        
        # Get country data
        cursor.execute("""
            SELECT name, code, phone_code, currency_id
            FROM res_country 
            ORDER BY name
            LIMIT 20
        """)
        
        countries = cursor.fetchall()
        
        print(f"Found {len(countries)} countries (showing first 20):")
        country_data = []
        
        for country in countries:
            country_info = {
                'name': country[0],
                'code': country[1],
                'phone_code': country[2],
                'currency_id': country[3]
            }
            country_data.append(country_info)
            print(f"  {country[1]} - {country[0]} (+{country[2]})")
        
        conn.close()
        return country_data
        
    except Exception as e:
        print(f"Error extracting countries: {e}")
        return []

def extract_chart_of_accounts():
    """Extract chart of accounts from Odoo"""
    print("\n🔍 Extracting chart of accounts from Odoo...")
    
    try:
        conn = psycopg2.connect(
            host='localhost',
            port='5432',
            database='erp',
            user='postgres',
            password='postgres'
        )
        cursor = conn.cursor()
        
        # First check what columns exist in account_account
        cursor.execute("""
            SELECT column_name FROM information_schema.columns
            WHERE table_name = 'account_account'
            ORDER BY ordinal_position
        """)
        columns = [row[0] for row in cursor.fetchall()]
        print(f"Available columns in account_account: {columns}")

        # Get account data with available columns
        cursor.execute("""
            SELECT code, name, reconcile, deprecated
            FROM account_account
            WHERE deprecated = false
            ORDER BY code
            LIMIT 30
        """)
        
        accounts = cursor.fetchall()
        
        print(f"Found {len(accounts)} active accounts (showing first 30):")
        account_data = []
        
        for account in accounts:
            account_info = {
                'code': account[0],
                'name': account[1],
                'reconcile': account[2],
                'deprecated': account[3]
            }
            account_data.append(account_info)
            print(f"  {account[0]} - {account[1]}")
        
        conn.close()
        return account_data
        
    except Exception as e:
        print(f"Error extracting accounts: {e}")
        return []

def extract_journals():
    """Extract journal data from Odoo"""
    print("\n🔍 Extracting journals from Odoo...")

    try:
        conn = psycopg2.connect(
            host='localhost',
            port='5432',
            database='erp',
            user='postgres',
            password='postgres'
        )
        cursor = conn.cursor()

        # Get journal data
        cursor.execute("""
            SELECT name, code, type, active
            FROM account_journal
            WHERE active = true
            ORDER BY name
        """)

        journals = cursor.fetchall()

        print(f"Found {len(journals)} active journals:")
        journal_data = []

        for journal in journals:
            journal_info = {
                'name': journal[0],
                'code': journal[1],
                'type': journal[2],
                'active': journal[3]
            }
            journal_data.append(journal_info)
            print(f"  {journal[1]} - {journal[0]} ({journal[2]})")

        conn.close()
        return journal_data

    except Exception as e:
        print(f"Error extracting journals: {e}")
        return []

def extract_uom_data():
    """Extract UOM categories and units from Odoo"""
    print("\n🔍 Extracting UOM data from Odoo...")

    try:
        conn = psycopg2.connect(
            host='localhost',
            port='5432',
            database='erp',
            user='postgres',
            password='postgres'
        )
        cursor = conn.cursor()

        # Get UOM categories
        cursor.execute("""
            SELECT name FROM uom_category
            ORDER BY name
        """)

        categories = cursor.fetchall()
        print(f"Found {len(categories)} UOM categories:")

        category_data = []
        for cat in categories:
            category_info = {'name': cat[0]}
            category_data.append(category_info)
            print(f"  - {cat[0]}")

        # Check UOM table structure first
        cursor.execute("""
            SELECT column_name FROM information_schema.columns
            WHERE table_name = 'uom_uom'
            ORDER BY ordinal_position
        """)
        uom_columns = [row[0] for row in cursor.fetchall()]
        print(f"Available columns in uom_uom: {uom_columns}")

        # Get UOM units with available columns
        cursor.execute("""
            SELECT u.name, u.factor, u.uom_type, u.active, c.name as category_name
            FROM uom_uom u
            JOIN uom_category c ON u.category_id = c.id
            WHERE u.active = true
            ORDER BY c.name, u.name
        """)

        uoms = cursor.fetchall()
        print(f"\nFound {len(uoms)} UOM units:")

        uom_data = []
        for uom in uoms:
            uom_info = {
                'name': uom[0],
                'factor': float(uom[1]) if uom[1] else 1.0,
                'uom_type': uom[2],
                'active': uom[3],
                'category_name': uom[4]
            }
            uom_data.append(uom_info)
            print(f"  {uom[4]}: {uom[0]} (factor: {uom[1]})")

        conn.close()
        return category_data, uom_data

    except Exception as e:
        print(f"Error extracting UOM data: {e}")
        return [], []

def main():
    """Main extraction function"""
    print("🚀 Odoo Data Extraction for Django ERP")
    print("=" * 50)

    # Extract all data
    currencies = extract_currencies()
    countries = extract_countries()
    accounts = extract_chart_of_accounts()
    journals = extract_journals()
    uom_categories, uoms = extract_uom_data()
    
    # Save to JSON files
    if currencies:
        with open('currencies_data.json', 'w') as f:
            json.dump(currencies, f, indent=2)
        print(f"\n✅ Saved {len(currencies)} currencies to currencies_data.json")
    
    if countries:
        with open('countries_data.json', 'w') as f:
            json.dump(countries, f, indent=2)
        print(f"✅ Saved {len(countries)} countries to countries_data.json")
    
    if accounts:
        with open('accounts_data.json', 'w') as f:
            json.dump(accounts, f, indent=2)
        print(f"✅ Saved {len(accounts)} accounts to accounts_data.json")

    if journals:
        with open('journals_data.json', 'w') as f:
            json.dump(journals, f, indent=2)
        print(f"✅ Saved {len(journals)} journals to journals_data.json")

    if uom_categories:
        with open('uom_categories_data.json', 'w') as f:
            json.dump(uom_categories, f, indent=2)
        print(f"✅ Saved {len(uom_categories)} UOM categories to uom_categories_data.json")

    if uoms:
        with open('uoms_data.json', 'w') as f:
            json.dump(uoms, f, indent=2)
        print(f"✅ Saved {len(uoms)} UOMs to uoms_data.json")

    print("\n🎉 Data extraction completed!")

if __name__ == "__main__":
    main()
