# Generated by Django 4.2.21 on 2025-07-20 09:24

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0003_country_phone_code'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('accounting', '0002_accountpayment'),
    ]

    operations = [
        migrations.CreateModel(
            name='AccountPaymentTerm',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(help_text='Payment Terms Name', max_length=255)),
                ('active', models.BooleanField(default=True)),
                ('sequence', models.IntegerField(default=10)),
                ('note', models.TextField(blank=True, help_text='Description on invoices')),
                ('company_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='AccountFiscalPosition',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(help_text='Fiscal Position Name', max_length=255)),
                ('active', models.BooleanField(default=True)),
                ('sequence', models.IntegerField(default=1)),
                ('vat_required', models.BooleanField(default=False, help_text='Apply only if partner has VAT')),
                ('auto_apply', models.BooleanField(default=False, help_text='Detect automatically')),
                ('note', models.TextField(blank=True, help_text='Notes')),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
            ],
        ),
    ]
