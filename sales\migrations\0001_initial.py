# Generated by Django 4.2.21 on 2025-07-19 15:49

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('accounting', '0001_initial'),
        ('core', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='AccountFiscalPosition',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=255)),
                ('active', models.BooleanField(default=True)),
                ('sequence', models.IntegerField(default=10)),
                ('auto_apply', models.BooleanField(default=False)),
                ('vat_required', models.BooleanField(default=False)),
                ('note', models.TextField(blank=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='AccountPaymentTerm',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=255)),
                ('active', models.BooleanField(default=True)),
                ('note', models.TextField(blank=True)),
                ('sequence', models.IntegerField(default=10)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='DeliveryCarrier',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=255)),
                ('active', models.BooleanField(default=True)),
                ('delivery_type', models.CharField(choices=[('fixed', 'Fixed Price'), ('base_on_rule', 'Based on Rules')], default='fixed', max_length=50)),
                ('fixed_price', models.DecimalField(decimal_places=2, default=0.0, max_digits=16)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='ProcurementGroup',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(max_length=255)),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('partner_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='core.partner')),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='ProductPricelist',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=255)),
                ('active', models.BooleanField(default=True)),
                ('sequence', models.IntegerField(default=16)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('currency_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.currency')),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='StockWarehouse',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=255)),
                ('code', models.CharField(max_length=10)),
                ('active', models.BooleanField(default=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('partner_id', models.ForeignKey(help_text='Address', on_delete=django.db.models.deletion.PROTECT, to='core.partner')),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'unique_together': {('code', 'company_id')},
            },
        ),
        migrations.CreateModel(
            name='SalesTeam',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=255)),
                ('code', models.CharField(blank=True, max_length=10)),
                ('active', models.BooleanField(default=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('member_ids', models.ManyToManyField(blank=True, related_name='sales_team_members', to=settings.AUTH_USER_MODEL)),
                ('user_id', models.ForeignKey(blank=True, help_text='Team Leader', null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='SaleOrder',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(default='New', help_text='Order Reference', max_length=255)),
                ('origin', models.CharField(blank=True, help_text='Source Document', max_length=255)),
                ('client_order_ref', models.CharField(blank=True, help_text='Customer Reference', max_length=255)),
                ('date_order', models.DateTimeField(default=django.utils.timezone.now, help_text='Order Date')),
                ('validity_date', models.DateField(blank=True, help_text='Expiration Date', null=True)),
                ('commitment_date', models.DateTimeField(blank=True, help_text='Delivery Date', null=True)),
                ('state', models.CharField(choices=[('draft', 'Quotation'), ('sent', 'Quotation Sent'), ('sale', 'Sales Order'), ('cancel', 'Cancelled')], db_index=True, default='draft', max_length=20)),
                ('locked', models.BooleanField(default=False, help_text='Locked orders cannot be modified')),
                ('amount_untaxed', models.DecimalField(decimal_places=2, default=0.0, max_digits=20)),
                ('amount_tax', models.DecimalField(decimal_places=2, default=0.0, max_digits=20)),
                ('amount_total', models.DecimalField(decimal_places=2, default=0.0, max_digits=20)),
                ('amount_undiscounted', models.DecimalField(decimal_places=2, default=0.0, max_digits=20)),
                ('invoice_status', models.CharField(choices=[('upselling', 'Upselling Opportunity'), ('invoiced', 'Fully Invoiced'), ('to invoice', 'To Invoice'), ('no', 'Nothing to Invoice')], default='no', max_length=20)),
                ('amount_to_invoice', models.DecimalField(decimal_places=2, default=0.0, max_digits=20)),
                ('amount_invoiced', models.DecimalField(decimal_places=2, default=0.0, max_digits=20)),
                ('note', models.TextField(blank=True, help_text='Terms and conditions')),
                ('carrier_id', models.ForeignKey(blank=True, help_text='Delivery Method', null=True, on_delete=django.db.models.deletion.SET_NULL, to='sales.deliverycarrier')),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('currency_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.currency')),
                ('fiscal_position_id', models.ForeignKey(blank=True, help_text='Fiscal Position', null=True, on_delete=django.db.models.deletion.SET_NULL, to='sales.accountfiscalposition')),
                ('partner_id', models.ForeignKey(help_text='Customer', on_delete=django.db.models.deletion.PROTECT, to='core.partner')),
                ('partner_invoice_id', models.ForeignKey(help_text='Invoice Address', on_delete=django.db.models.deletion.PROTECT, related_name='sale_invoice_orders', to='core.partner')),
                ('partner_shipping_id', models.ForeignKey(help_text='Delivery Address', on_delete=django.db.models.deletion.PROTECT, related_name='sale_shipping_orders', to='core.partner')),
                ('payment_term_id', models.ForeignKey(blank=True, help_text='Payment Terms', null=True, on_delete=django.db.models.deletion.SET_NULL, to='sales.accountpaymentterm')),
                ('pricelist_id', models.ForeignKey(help_text='Pricelist', on_delete=django.db.models.deletion.PROTECT, to='sales.productpricelist')),
                ('procurement_group_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='sales.procurementgroup')),
                ('team_id', models.ForeignKey(blank=True, help_text='Sales Team', null=True, on_delete=django.db.models.deletion.SET_NULL, to='sales.salesteam')),
                ('user_id', models.ForeignKey(blank=True, help_text='Salesperson', null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('warehouse_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='sales.stockwarehouse')),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='ProductUomCategory',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(max_length=255)),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='ProductUom',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=255)),
                ('factor', models.DecimalField(decimal_places=6, default=1.0, max_digits=16)),
                ('factor_inv', models.DecimalField(decimal_places=6, default=1.0, max_digits=16)),
                ('uom_type', models.CharField(choices=[('bigger', 'Bigger than the reference Unit of Measure'), ('reference', 'Reference Unit of Measure for this category'), ('smaller', 'Smaller than the reference Unit of Measure')], default='reference', max_length=20)),
                ('rounding', models.DecimalField(decimal_places=6, default=0.01, max_digits=16)),
                ('active', models.BooleanField(default=True)),
                ('category_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='sales.productuomcategory')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='SaleOrderLine',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.TextField(help_text='Description')),
                ('product_uom_qty', models.DecimalField(decimal_places=4, default=1.0, help_text='Ordered Quantity', max_digits=16)),
                ('qty_delivered', models.DecimalField(decimal_places=4, default=0.0, help_text='Delivered Quantity', max_digits=16)),
                ('qty_invoiced', models.DecimalField(decimal_places=4, default=0.0, help_text='Invoiced Quantity', max_digits=16)),
                ('qty_to_invoice', models.DecimalField(decimal_places=4, default=0.0, help_text='To Invoice Quantity', max_digits=16)),
                ('price_unit', models.DecimalField(decimal_places=4, default=0.0, help_text='Unit Price', max_digits=20)),
                ('discount', models.DecimalField(decimal_places=2, default=0.0, help_text='Discount (%)', max_digits=16)),
                ('price_subtotal', models.DecimalField(decimal_places=2, default=0.0, help_text='Subtotal', max_digits=20)),
                ('price_tax', models.DecimalField(decimal_places=2, default=0.0, help_text='Tax Amount', max_digits=20)),
                ('price_total', models.DecimalField(decimal_places=2, default=0.0, help_text='Total', max_digits=20)),
                ('price_reduce', models.DecimalField(decimal_places=4, default=0.0, help_text='Unit Price with Discount', max_digits=20)),
                ('price_reduce_taxexcl', models.DecimalField(decimal_places=4, default=0.0, help_text='Unit Price with Discount (Tax Excluded)', max_digits=20)),
                ('customer_lead', models.IntegerField(default=0, help_text='Lead Time (days)')),
                ('sequence', models.IntegerField(default=10, help_text='Sequence')),
                ('display_type', models.CharField(blank=True, choices=[('line_section', 'Section'), ('line_note', 'Note')], help_text='Technical field for UX purpose', max_length=20, null=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('currency_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.currency')),
                ('order_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='order_line', to='sales.saleorder')),
                ('product_uom', models.ForeignKey(blank=True, help_text='Unit of Measure', null=True, on_delete=django.db.models.deletion.PROTECT, to='sales.productuom')),
                ('tax_id', models.ManyToManyField(blank=True, help_text='Taxes', to='accounting.accounttax')),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'indexes': [models.Index(fields=['order_id'], name='sales_saleo_order_i_083ae3_idx'), models.Index(fields=['sequence'], name='sales_saleo_sequenc_df64ed_idx')],
            },
        ),
        migrations.AddIndex(
            model_name='saleorder',
            index=models.Index(fields=['date_order'], name='sales_saleo_date_or_c2ee01_idx'),
        ),
        migrations.AddIndex(
            model_name='saleorder',
            index=models.Index(fields=['state'], name='sales_saleo_state_31bbbf_idx'),
        ),
        migrations.AddIndex(
            model_name='saleorder',
            index=models.Index(fields=['partner_id'], name='sales_saleo_partner_429ac2_idx'),
        ),
        migrations.AddIndex(
            model_name='saleorder',
            index=models.Index(fields=['user_id'], name='sales_saleo_user_id_a74ee9_idx'),
        ),
        migrations.AddConstraint(
            model_name='saleorder',
            constraint=models.CheckConstraint(check=models.Q(models.Q(models.Q(('date_order__isnull', False), ('state', 'sale')), models.Q(('state', 'sale'), _negated=True), _connector='OR')), name='date_order_conditional_required'),
        ),
    ]
