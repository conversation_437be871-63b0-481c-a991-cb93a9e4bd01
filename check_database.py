#!/usr/bin/env python
"""
Database Verification Script for Django ERP
Checks PostgreSQL setup and tables
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'django_erp.settings')
django.setup()

from django.db import connection
from django.apps import apps

def check_database_connection():
    """Check database connection and details"""
    print("🔍 Database Connection Check")
    print("=" * 40)
    
    try:
        with connection.cursor() as cursor:
            cursor.execute("SELECT version()")
            version = cursor.fetchone()[0]
            
        print(f"✅ Database: {connection.settings_dict['NAME']}")
        print(f"✅ Host: {connection.settings_dict['HOST']}:{connection.settings_dict['PORT']}")
        print(f"✅ Engine: {connection.settings_dict['ENGINE']}")
        print(f"✅ PostgreSQL Version: {version[:50]}...")
        return True
        
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

def check_tables():
    """Check created tables"""
    print("\n📊 Database Tables Check")
    print("=" * 40)
    
    try:
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_type = 'BASE TABLE'
                ORDER BY table_name
            """)
            tables = cursor.fetchall()
        
        print(f"Total tables created: {len(tables)}")
        print("\nDjango ERP Tables:")
        
        # Group tables by module
        modules = {
            'core': [],
            'accounting': [],
            'sales': [],
            'purchases': [],
            'inventory': [],
            'auth': [],
            'admin': [],
            'other': []
        }
        
        for table in tables:
            table_name = table[0]
            if table_name.startswith('core_'):
                modules['core'].append(table_name)
            elif table_name.startswith('accounting_'):
                modules['accounting'].append(table_name)
            elif table_name.startswith('sales_'):
                modules['sales'].append(table_name)
            elif table_name.startswith('purchases_'):
                modules['purchases'].append(table_name)
            elif table_name.startswith('inventory_'):
                modules['inventory'].append(table_name)
            elif table_name.startswith('auth_'):
                modules['auth'].append(table_name)
            elif table_name.startswith('django_admin'):
                modules['admin'].append(table_name)
            else:
                modules['other'].append(table_name)
        
        for module, table_list in modules.items():
            if table_list:
                print(f"\n📦 {module.upper()} Module ({len(table_list)} tables):")
                for table in table_list:
                    print(f"  ✅ {table}")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to check tables: {e}")
        return False

def check_models():
    """Check Django models"""
    print("\n🏗️  Django Models Check")
    print("=" * 40)
    
    try:
        erp_apps = ['core', 'accounting', 'sales', 'purchases', 'inventory']
        total_models = 0
        
        for app_name in erp_apps:
            try:
                app_config = apps.get_app_config(app_name)
                models = app_config.get_models()
                print(f"📦 {app_name.upper()}: {len(models)} models")
                total_models += len(models)
                
                for model in models:
                    print(f"  ✅ {model.__name__}")
                    
            except Exception as e:
                print(f"  ❌ Error loading {app_name}: {e}")
        
        print(f"\nTotal ERP models: {total_models}")
        return True
        
    except Exception as e:
        print(f"❌ Failed to check models: {e}")
        return False

def check_migrations():
    """Check migration status"""
    print("\n🔄 Migration Status Check")
    print("=" * 40)
    
    try:
        from django.db.migrations.executor import MigrationExecutor
        executor = MigrationExecutor(connection)
        
        # Get migration plan
        plan = executor.migration_plan(executor.loader.graph.leaf_nodes())
        
        if plan:
            print("❌ Pending migrations found:")
            for migration, backwards in plan:
                print(f"  - {migration}")
        else:
            print("✅ All migrations applied successfully")
            
        # Show applied migrations
        applied = executor.loader.applied_migrations
        print(f"✅ Total applied migrations: {len(applied)}")
        
        return len(plan) == 0
        
    except Exception as e:
        print(f"❌ Failed to check migrations: {e}")
        return False

def test_model_operations():
    """Test basic model operations"""
    print("\n🧪 Model Operations Test")
    print("=" * 40)
    
    try:
        from core.models import Company, Currency, Country
        from accounting.models import AccountJournal
        
        # Test model imports
        print("✅ Model imports successful")
        
        # Test database queries
        company_count = Company.objects.count()
        currency_count = Currency.objects.count()
        country_count = Country.objects.count()
        journal_count = AccountJournal.objects.count()
        
        print(f"✅ Companies: {company_count}")
        print(f"✅ Currencies: {currency_count}")
        print(f"✅ Countries: {country_count}")
        print(f"✅ Journals: {journal_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ Model operations failed: {e}")
        return False

def main():
    """Main verification function"""
    print("🚀 Django ERP PostgreSQL Verification")
    print("=" * 60)
    
    # Run all checks
    checks = [
        ("Database Connection", check_database_connection),
        ("Database Tables", check_tables),
        ("Django Models", check_models),
        ("Migration Status", check_migrations),
        ("Model Operations", test_model_operations),
    ]
    
    results = []
    for check_name, check_func in checks:
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"❌ {check_name} failed with error: {e}")
            results.append((check_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 VERIFICATION SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for check_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {check_name}")
    
    print(f"\nOverall: {passed}/{total} checks passed")
    
    if passed == total:
        print("\n🎉 ALL CHECKS PASSED!")
        print("✅ Django ERP is successfully running on PostgreSQL")
        print("✅ Database: django_erp")
        print("✅ All tables created")
        print("✅ All models working")
        print("\n🚀 Ready for development!")
    else:
        print(f"\n⚠️  {total - passed} checks failed")
        print("Please review the errors above")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
