# Generated by Django 4.2.21 on 2025-07-19 16:18

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('sales', '0001_initial'),
        ('purchases', '0001_initial'),
        ('core', '0001_initial'),
        ('accounting', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Product',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('default_code', models.CharField(blank=True, help_text='Internal Reference', max_length=100)),
                ('barcode', models.Char<PERSON><PERSON>(blank=True, max_length=100, unique=True)),
                ('qty_available', models.DecimalField(decimal_places=4, default=0.0, help_text='Quantity On Hand', max_digits=16)),
                ('virtual_available', models.DecimalField(decimal_places=4, default=0.0, help_text='Forecasted Quantity', max_digits=16)),
                ('incoming_qty', models.DecimalField(decimal_places=4, default=0.0, help_text='Incoming Quantity', max_digits=16)),
                ('outgoing_qty', models.DecimalField(decimal_places=4, default=0.0, help_text='Outgoing Quantity', max_digits=16)),
                ('active', models.BooleanField(default=True)),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='ProductCategory',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(db_index=True, max_length=255)),
                ('complete_name', models.CharField(blank=True, max_length=500)),
                ('parent_path', models.CharField(blank=True, db_index=True, max_length=500)),
                ('property_valuation', models.CharField(choices=[('manual_periodic', 'Manual'), ('real_time', 'Automated')], default='manual_periodic', max_length=20)),
                ('property_cost_method', models.CharField(choices=[('standard', 'Standard Price'), ('fifo', 'First In First Out (FIFO)'), ('average', 'Average Cost (AVCO)')], default='standard', max_length=20)),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('parent_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='inventory.productcategory')),
                ('property_stock_account_input_categ_id', models.ForeignKey(blank=True, help_text='Stock Input Account', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='category_stock_input', to='accounting.accountaccount')),
                ('property_stock_account_output_categ_id', models.ForeignKey(blank=True, help_text='Stock Output Account', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='category_stock_output', to='accounting.accountaccount')),
                ('property_stock_valuation_account_id', models.ForeignKey(blank=True, help_text='Stock Valuation Account', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='category_stock_valuation', to='accounting.accountaccount')),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name_plural': 'Product Categories',
            },
        ),
        migrations.CreateModel(
            name='StockInventory',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(max_length=255)),
                ('date', models.DateTimeField(default=django.utils.timezone.now)),
                ('state', models.CharField(choices=[('draft', 'Draft'), ('cancel', 'Cancelled'), ('confirm', 'In Progress'), ('done', 'Validated')], default='draft', max_length=20)),
                ('category_ids', models.ManyToManyField(blank=True, to='inventory.productcategory')),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='StockLocation',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=255)),
                ('complete_name', models.CharField(blank=True, max_length=500)),
                ('active', models.BooleanField(default=True)),
                ('usage', models.CharField(choices=[('supplier', 'Vendor Location'), ('view', 'View'), ('internal', 'Internal Location'), ('customer', 'Customer Location'), ('inventory', 'Inventory Loss'), ('production', 'Production'), ('transit', 'Transit Location')], db_index=True, default='internal', max_length=20)),
                ('parent_path', models.CharField(blank=True, db_index=True, max_length=500)),
                ('posx', models.IntegerField(default=0, help_text='Corridor (X)')),
                ('posy', models.IntegerField(default=0, help_text='Shelves (Y)')),
                ('posz', models.IntegerField(default=0, help_text='Height (Z)')),
                ('barcode', models.CharField(blank=True, max_length=100)),
                ('comment', models.TextField(blank=True, help_text='Additional Information')),
                ('company_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('location_id', models.ForeignKey(blank=True, help_text='Parent Location', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='child_locations', to='inventory.stocklocation')),
            ],
        ),
        migrations.CreateModel(
            name='StockLot',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(help_text='Lot/Serial Number', max_length=255)),
                ('ref', models.CharField(blank=True, help_text='Internal Reference', max_length=255)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('use_date', models.DateField(blank=True, help_text='Best before Date', null=True)),
                ('removal_date', models.DateField(blank=True, help_text='Removal Date', null=True)),
                ('alert_date', models.DateField(blank=True, help_text='Alert Date', null=True)),
                ('note', models.TextField(blank=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('product_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='inventory.product')),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='StockPackageType',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(max_length=255)),
                ('sequence', models.IntegerField(default=1)),
                ('height', models.IntegerField(default=0, help_text='Height in mm')),
                ('width', models.IntegerField(default=0, help_text='Width in mm')),
                ('packaging_length', models.IntegerField(default=0, help_text='Length in mm')),
                ('max_weight', models.DecimalField(decimal_places=4, default=0.0, help_text='Max Weight in kg', max_digits=16)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='StockRule',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=255)),
                ('active', models.BooleanField(default=True)),
                ('action', models.CharField(choices=[('pull', 'Pull From'), ('push', 'Push To'), ('pull_push', 'Pull & Push'), ('buy', 'Buy'), ('manufacture', 'Manufacture')], max_length=20)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('location_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='inventory.stocklocation')),
                ('location_src_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='rule_src', to='inventory.stocklocation')),
                ('picking_type_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='purchases.stockpickingtype')),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='StockRemovalStrategy',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(max_length=255)),
                ('method', models.CharField(choices=[('fifo', 'First In First Out (FIFO)'), ('lifo', 'Last In First Out (LIFO)'), ('closest', 'Closest Location'), ('fefo', 'First Expiry First Out (FEFO)')], default='fifo', max_length=20)),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='StockQuantPackage',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(help_text='Package Reference', max_length=255)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('location_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='inventory.stocklocation')),
                ('package_type_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='inventory.stockpackagetype')),
                ('parent_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='child_packages', to='inventory.stockquantpackage')),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='StockPicking',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(default='New', max_length=255)),
                ('origin', models.CharField(blank=True, help_text='Source Document', max_length=255)),
                ('state', models.CharField(choices=[('draft', 'Draft'), ('waiting', 'Waiting Another Operation'), ('confirmed', 'Waiting'), ('assigned', 'Ready'), ('done', 'Done'), ('cancel', 'Cancelled')], db_index=True, default='draft', max_length=20)),
                ('move_type', models.CharField(choices=[('direct', 'As soon as possible'), ('one', 'When all products are ready')], default='direct', max_length=20)),
                ('scheduled_date', models.DateTimeField(default=django.utils.timezone.now, help_text='Scheduled Date')),
                ('date_done', models.DateTimeField(blank=True, help_text='Date Done', null=True)),
                ('priority', models.CharField(choices=[('0', 'Normal'), ('1', 'Urgent')], default='0', max_length=1)),
                ('note', models.TextField(blank=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('location_dest_id', models.ForeignKey(help_text='Destination Location', on_delete=django.db.models.deletion.PROTECT, related_name='picking_dest', to='inventory.stocklocation')),
                ('location_id', models.ForeignKey(help_text='Source Location', on_delete=django.db.models.deletion.PROTECT, to='inventory.stocklocation')),
                ('partner_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='core.partner')),
                ('picking_type_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='purchases.stockpickingtype')),
                ('user_id', models.ForeignKey(blank=True, help_text='Responsible', null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='StockMove',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(help_text='Description', max_length=255)),
                ('sequence', models.IntegerField(default=10)),
                ('product_uom_qty', models.DecimalField(decimal_places=4, default=1.0, help_text='Demand', max_digits=16)),
                ('product_qty', models.DecimalField(decimal_places=4, default=1.0, help_text='Real Quantity', max_digits=16)),
                ('quantity_done', models.DecimalField(decimal_places=4, default=0.0, help_text='Quantity Done', max_digits=16)),
                ('state', models.CharField(choices=[('draft', 'New'), ('waiting', 'Waiting Another Move'), ('confirmed', 'Waiting Availability'), ('partially_available', 'Partially Available'), ('assigned', 'Available'), ('done', 'Done'), ('cancel', 'Cancelled')], db_index=True, default='draft', max_length=20)),
                ('date', models.DateTimeField(default=django.utils.timezone.now, help_text='Date')),
                ('date_deadline', models.DateTimeField(blank=True, help_text='Deadline', null=True)),
                ('origin', models.CharField(blank=True, max_length=255)),
                ('reference', models.CharField(blank=True, max_length=255)),
                ('description_picking', models.TextField(blank=True)),
                ('priority', models.CharField(choices=[('0', 'Normal'), ('1', 'Urgent')], default='0', max_length=1)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('group_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='sales.procurementgroup')),
                ('location_dest_id', models.ForeignKey(help_text='Destination Location', on_delete=django.db.models.deletion.PROTECT, related_name='move_dest', to='inventory.stocklocation')),
                ('location_id', models.ForeignKey(help_text='Source Location', on_delete=django.db.models.deletion.PROTECT, to='inventory.stocklocation')),
                ('move_orig_ids', models.ManyToManyField(blank=True, help_text='Original Moves', related_name='move_dest_ids', to='inventory.stockmove')),
                ('partner_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='core.partner')),
                ('picking_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='move_ids', to='inventory.stockpicking')),
                ('product_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='inventory.product')),
                ('product_uom', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='sales.productuom')),
                ('rule_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='inventory.stockrule')),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.AddField(
            model_name='stocklocation',
            name='removal_strategy_id',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='inventory.stockremovalstrategy'),
        ),
        migrations.AddField(
            model_name='stocklocation',
            name='valuation_in_account_id',
            field=models.ForeignKey(blank=True, help_text='Used for real-time inventory valuation', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='location_valuation_in', to='accounting.accountaccount'),
        ),
        migrations.AddField(
            model_name='stocklocation',
            name='valuation_out_account_id',
            field=models.ForeignKey(blank=True, help_text='Used for real-time inventory valuation', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='location_valuation_out', to='accounting.accountaccount'),
        ),
        migrations.AddField(
            model_name='stocklocation',
            name='write_uid',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL),
        ),
        migrations.CreateModel(
            name='StockInventoryLine',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('theoretical_qty', models.DecimalField(decimal_places=4, default=0.0, help_text='Theoretical Quantity', max_digits=16)),
                ('product_qty', models.DecimalField(decimal_places=4, default=0.0, help_text='Real Quantity', max_digits=16)),
                ('difference_qty', models.DecimalField(decimal_places=4, default=0.0, help_text='Difference', max_digits=16)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('inventory_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='line_ids', to='inventory.stockinventory')),
                ('location_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='inventory.stocklocation')),
                ('lot_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='inventory.stocklot')),
                ('package_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='inventory.stockquantpackage')),
                ('product_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='inventory.product')),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.AddField(
            model_name='stockinventory',
            name='location_ids',
            field=models.ManyToManyField(blank=True, to='inventory.stocklocation'),
        ),
        migrations.AddField(
            model_name='stockinventory',
            name='product_ids',
            field=models.ManyToManyField(blank=True, to='inventory.product'),
        ),
        migrations.AddField(
            model_name='stockinventory',
            name='write_uid',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL),
        ),
        migrations.CreateModel(
            name='ProductTemplate',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(db_index=True, max_length=255)),
                ('default_code', models.CharField(blank=True, help_text='Internal Reference', max_length=100)),
                ('barcode', models.CharField(blank=True, max_length=100, unique=True)),
                ('detailed_type', models.CharField(choices=[('consu', 'Consumable'), ('service', 'Service'), ('product', 'Storable Product')], default='consu', max_length=20)),
                ('list_price', models.DecimalField(decimal_places=4, default=0.0, help_text='Sales Price', max_digits=20)),
                ('standard_price', models.DecimalField(decimal_places=4, default=0.0, help_text='Cost Price', max_digits=20)),
                ('tracking', models.CharField(choices=[('serial', 'By Unique Serial Number'), ('lot', 'By Lots'), ('none', 'No Tracking')], default='none', max_length=20)),
                ('description', models.TextField(blank=True, help_text='Internal Notes')),
                ('description_sale', models.TextField(blank=True, help_text='Sales Description')),
                ('description_purchase', models.TextField(blank=True, help_text='Purchase Description')),
                ('description_picking', models.TextField(blank=True, help_text='Description on Picking')),
                ('description_pickingout', models.TextField(blank=True, help_text='Description on Delivery Orders')),
                ('description_pickingin', models.TextField(blank=True, help_text='Description on Receptions')),
                ('qty_available', models.DecimalField(decimal_places=4, default=0.0, help_text='Quantity On Hand', max_digits=16)),
                ('virtual_available', models.DecimalField(decimal_places=4, default=0.0, help_text='Forecasted Quantity', max_digits=16)),
                ('incoming_qty', models.DecimalField(decimal_places=4, default=0.0, help_text='Incoming Quantity', max_digits=16)),
                ('outgoing_qty', models.DecimalField(decimal_places=4, default=0.0, help_text='Outgoing Quantity', max_digits=16)),
                ('active', models.BooleanField(default=True)),
                ('categ_id', models.ForeignKey(help_text='Product Category', on_delete=django.db.models.deletion.PROTECT, to='inventory.productcategory')),
                ('company_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('responsible_id', models.ForeignKey(blank=True, help_text='Responsible Person', null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('uom_id', models.ForeignKey(help_text='Unit of Measure', on_delete=django.db.models.deletion.PROTECT, to='sales.productuom')),
                ('uom_po_id', models.ForeignKey(help_text='Purchase Unit of Measure', on_delete=django.db.models.deletion.PROTECT, related_name='product_template_po', to='sales.productuom')),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.AddField(
            model_name='product',
            name='product_tmpl_id',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='product_variant_ids', to='inventory.producttemplate'),
        ),
        migrations.AddField(
            model_name='product',
            name='write_uid',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL),
        ),
        migrations.CreateModel(
            name='IrSequence',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(max_length=255)),
                ('code', models.CharField(max_length=100)),
                ('prefix', models.CharField(blank=True, max_length=50)),
                ('suffix', models.CharField(blank=True, max_length=50)),
                ('number_next', models.IntegerField(default=1)),
                ('number_increment', models.IntegerField(default=1)),
                ('padding', models.IntegerField(default=4)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='StockQuant',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('quantity', models.DecimalField(decimal_places=4, default=0.0, help_text='Quantity', max_digits=16)),
                ('reserved_quantity', models.DecimalField(decimal_places=4, default=0.0, help_text='Reserved Quantity', max_digits=16)),
                ('available_quantity', models.DecimalField(decimal_places=4, default=0.0, help_text='Available Quantity', max_digits=16)),
                ('in_date', models.DateTimeField(default=django.utils.timezone.now, help_text='Incoming Date')),
                ('value', models.DecimalField(decimal_places=2, default=0.0, help_text='Inventory Value', max_digits=20)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('currency_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='core.currency')),
                ('location_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='inventory.stocklocation')),
                ('lot_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='inventory.stocklot')),
                ('owner_id', models.ForeignKey(blank=True, help_text='Owner', null=True, on_delete=django.db.models.deletion.PROTECT, to='core.partner')),
                ('package_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='inventory.stockquantpackage')),
                ('product_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='inventory.product')),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'indexes': [models.Index(fields=['product_id', 'location_id'], name='inventory_s_product_e81e76_idx'), models.Index(fields=['location_id'], name='inventory_s_locatio_89a6c9_idx')],
            },
        ),
        migrations.AddConstraint(
            model_name='stockquant',
            constraint=models.UniqueConstraint(fields=('product_id', 'location_id', 'lot_id', 'package_id', 'owner_id'), name='stock_quant_unique'),
        ),
        migrations.AddIndex(
            model_name='stockpicking',
            index=models.Index(fields=['state'], name='inventory_s_state_d3a102_idx'),
        ),
        migrations.AddIndex(
            model_name='stockpicking',
            index=models.Index(fields=['scheduled_date'], name='inventory_s_schedul_0fda34_idx'),
        ),
        migrations.AddIndex(
            model_name='stockpicking',
            index=models.Index(fields=['picking_type_id'], name='inventory_s_picking_cc1f53_idx'),
        ),
        migrations.AddIndex(
            model_name='stockmove',
            index=models.Index(fields=['state'], name='inventory_s_state_4128a7_idx'),
        ),
        migrations.AddIndex(
            model_name='stockmove',
            index=models.Index(fields=['date'], name='inventory_s_date_0c801d_idx'),
        ),
        migrations.AddIndex(
            model_name='stockmove',
            index=models.Index(fields=['product_id'], name='inventory_s_product_f8ad8d_idx'),
        ),
        migrations.AddIndex(
            model_name='stockmove',
            index=models.Index(fields=['picking_id'], name='inventory_s_picking_324fda_idx'),
        ),
        migrations.AddIndex(
            model_name='stocklot',
            index=models.Index(fields=['product_id'], name='inventory_s_product_bc4bf8_idx'),
        ),
        migrations.AddIndex(
            model_name='stocklot',
            index=models.Index(fields=['name'], name='inventory_s_name_1f8600_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='stocklot',
            unique_together={('name', 'product_id', 'company_id')},
        ),
        migrations.AddIndex(
            model_name='stocklocation',
            index=models.Index(fields=['usage'], name='inventory_s_usage_3a69d4_idx'),
        ),
        migrations.AddIndex(
            model_name='stocklocation',
            index=models.Index(fields=['parent_path'], name='inventory_s_parent__6b0fa8_idx'),
        ),
        migrations.AddConstraint(
            model_name='stocklocation',
            constraint=models.UniqueConstraint(condition=models.Q(('barcode__isnull', False)), fields=('barcode', 'company_id'), name='barcode_company_uniq'),
        ),
        migrations.AddIndex(
            model_name='producttemplate',
            index=models.Index(fields=['name'], name='inventory_p_name_44178f_idx'),
        ),
        migrations.AddIndex(
            model_name='producttemplate',
            index=models.Index(fields=['default_code'], name='inventory_p_default_40f998_idx'),
        ),
        migrations.AddIndex(
            model_name='producttemplate',
            index=models.Index(fields=['detailed_type'], name='inventory_p_detaile_368c8e_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['product_tmpl_id'], name='inventory_p_product_43c86b_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['default_code'], name='inventory_p_default_b9bbbb_idx'),
        ),
    ]
