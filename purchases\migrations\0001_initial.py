# Generated by Django 4.2.21 on 2025-07-19 16:04

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('sales', '0001_initial'),
        ('core', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('accounting', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='StockPickingType',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=255)),
                ('code', models.CharField(choices=[('incoming', 'Receipt'), ('outgoing', 'Delivery'), ('internal', 'Internal Transfer')], max_length=20)),
                ('sequence', models.IntegerField(default=1)),
                ('active', models.BooleanField(default=True)),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('warehouse_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='sales.stockwarehouse')),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='PurchaseOrder',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(default='New', help_text='Order Reference', max_length=255)),
                ('origin', models.CharField(blank=True, help_text='Source Document', max_length=255)),
                ('partner_ref', models.CharField(blank=True, help_text='Vendor Reference', max_length=255)),
                ('priority', models.CharField(choices=[('0', 'Normal'), ('1', 'Urgent')], db_index=True, default='0', max_length=1)),
                ('date_order', models.DateTimeField(default=django.utils.timezone.now, help_text='Order Deadline')),
                ('date_approve', models.DateTimeField(blank=True, help_text='Confirmation Date', null=True)),
                ('date_planned', models.DateTimeField(blank=True, help_text='Expected Arrival', null=True)),
                ('state', models.CharField(choices=[('draft', 'RFQ'), ('sent', 'RFQ Sent'), ('to approve', 'To Approve'), ('purchase', 'Purchase Order'), ('done', 'Locked'), ('cancel', 'Cancelled')], db_index=True, default='draft', max_length=20)),
                ('amount_untaxed', models.DecimalField(decimal_places=2, default=0.0, max_digits=20)),
                ('amount_tax', models.DecimalField(decimal_places=2, default=0.0, max_digits=20)),
                ('amount_total', models.DecimalField(decimal_places=2, default=0.0, max_digits=20)),
                ('invoice_status', models.CharField(choices=[('no', 'Nothing to Bill'), ('to invoice', 'Waiting Bills'), ('invoiced', 'Fully Billed')], default='no', max_length=20)),
                ('notes', models.TextField(blank=True, help_text='Terms and conditions')),
                ('receipt_status', models.CharField(choices=[('pending', 'Not Received'), ('partial', 'Partially Received'), ('full', 'Fully Received')], default='pending', max_length=20)),
                ('mail_reminder_confirmed', models.BooleanField(default=False)),
                ('mail_reception_confirmed', models.BooleanField(default=False)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('currency_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.currency')),
                ('fiscal_position_id', models.ForeignKey(blank=True, help_text='Fiscal Position', null=True, on_delete=django.db.models.deletion.SET_NULL, to='sales.accountfiscalposition')),
                ('group_id', models.ForeignKey(blank=True, help_text='Procurement Group', null=True, on_delete=django.db.models.deletion.SET_NULL, to='sales.procurementgroup')),
                ('partner_id', models.ForeignKey(help_text='Vendor', on_delete=django.db.models.deletion.PROTECT, to='core.partner')),
                ('payment_term_id', models.ForeignKey(blank=True, help_text='Payment Terms', null=True, on_delete=django.db.models.deletion.SET_NULL, to='sales.accountpaymentterm')),
                ('picking_type_id', models.ForeignKey(blank=True, help_text='Deliver To', null=True, on_delete=django.db.models.deletion.SET_NULL, to='purchases.stockpickingtype')),
                ('user_id', models.ForeignKey(blank=True, help_text='Purchase Representative', null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='PurchaseApprovalSettings',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('po_double_validation', models.CharField(choices=[('one_step', 'Confirm purchase orders in one step'), ('two_step', 'Get 2 levels of approvals to confirm a purchase order')], default='one_step', help_text='Purchase Order Approval', max_length=20)),
                ('po_double_validation_amount', models.DecimalField(decimal_places=2, default=5000.0, help_text='Minimum amount for requiring two approvals', max_digits=20)),
                ('po_lock', models.CharField(choices=[('edit', 'Allow to edit purchase orders'), ('lock', 'Confirmed purchase orders are not editable')], default='edit', help_text='Purchase Order Modification', max_length=10)),
                ('po_lead', models.FloatField(default=0.0, help_text='Purchase Lead Time (days)')),
                ('company_id', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='purchase_settings', to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='PurchaseOrderLine',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.TextField(help_text='Description')),
                ('product_qty', models.DecimalField(decimal_places=4, default=1.0, help_text='Quantity', max_digits=16)),
                ('product_uom_qty', models.DecimalField(decimal_places=4, default=1.0, help_text='Total Quantity', max_digits=16)),
                ('qty_received', models.DecimalField(decimal_places=4, default=0.0, help_text='Received Quantity', max_digits=16)),
                ('qty_invoiced', models.DecimalField(decimal_places=4, default=0.0, help_text='Billed Quantity', max_digits=16)),
                ('qty_to_invoice', models.DecimalField(decimal_places=4, default=0.0, help_text='To Bill Quantity', max_digits=16)),
                ('price_unit', models.DecimalField(decimal_places=4, default=0.0, help_text='Unit Price', max_digits=20)),
                ('discount', models.DecimalField(decimal_places=2, default=0.0, help_text='Discount (%)', max_digits=16)),
                ('price_subtotal', models.DecimalField(decimal_places=2, default=0.0, help_text='Subtotal', max_digits=20)),
                ('price_tax', models.DecimalField(decimal_places=2, default=0.0, help_text='Tax Amount', max_digits=20)),
                ('price_total', models.DecimalField(decimal_places=2, default=0.0, help_text='Total', max_digits=20)),
                ('date_planned', models.DateTimeField(help_text='Expected Arrival')),
                ('sequence', models.IntegerField(default=10, help_text='Sequence')),
                ('display_type', models.CharField(blank=True, choices=[('line_section', 'Section'), ('line_note', 'Note')], help_text='Technical field for UX purpose', max_length=20, null=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('currency_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.currency')),
                ('order_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='order_line', to='purchases.purchaseorder')),
                ('partner_id', models.ForeignKey(help_text='Vendor', on_delete=django.db.models.deletion.PROTECT, to='core.partner')),
                ('product_uom', models.ForeignKey(blank=True, help_text='Unit of Measure', null=True, on_delete=django.db.models.deletion.PROTECT, to='sales.productuom')),
                ('taxes_id', models.ManyToManyField(blank=True, help_text='Taxes', to='accounting.accounttax')),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'indexes': [models.Index(fields=['order_id'], name='purchases_p_order_i_b280c9_idx'), models.Index(fields=['sequence'], name='purchases_p_sequenc_021a01_idx'), models.Index(fields=['date_planned'], name='purchases_p_date_pl_38b48e_idx')],
            },
        ),
        migrations.AddIndex(
            model_name='purchaseorder',
            index=models.Index(fields=['date_order'], name='purchases_p_date_or_aab4a5_idx'),
        ),
        migrations.AddIndex(
            model_name='purchaseorder',
            index=models.Index(fields=['state'], name='purchases_p_state_c00221_idx'),
        ),
        migrations.AddIndex(
            model_name='purchaseorder',
            index=models.Index(fields=['partner_id'], name='purchases_p_partner_70666a_idx'),
        ),
        migrations.AddIndex(
            model_name='purchaseorder',
            index=models.Index(fields=['user_id'], name='purchases_p_user_id_8b421a_idx'),
        ),
        migrations.AddIndex(
            model_name='purchaseorder',
            index=models.Index(fields=['priority'], name='purchases_p_priorit_9c67c4_idx'),
        ),
    ]
