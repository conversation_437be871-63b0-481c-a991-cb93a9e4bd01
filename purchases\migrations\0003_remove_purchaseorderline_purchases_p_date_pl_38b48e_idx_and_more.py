# Generated by Django 4.2.21 on 2025-07-20 09:58

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0003_productpackaging'),
        ('accounting', '0005_accountincoterms'),
        ('core', '0004_rename_state_countrystate_countrygroup'),
        ('purchases', '0002_alter_purchaseorder_fiscal_position_id_and_more'),
    ]

    operations = [
        migrations.RemoveIndex(
            model_name='purchaseorderline',
            name='purchases_p_date_pl_38b48e_idx',
        ),
        migrations.AddField(
            model_name='purchaseorder',
            name='currency_rate',
            field=models.FloatField(default=1.0, help_text='Currency Rate'),
        ),
        migrations.AddField(
            model_name='purchaseorder',
            name='dest_address_id',
            field=models.ForeignKey(blank=True, help_text='Dropship Address', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='purchase_dest_orders', to='core.partner'),
        ),
        migrations.AddField(
            model_name='purchaseorder',
            name='incoterm_id',
            field=models.ForeignKey(blank=True, help_text='International Commercial Terms', null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.accountincoterms'),
        ),
        migrations.AddField(
            model_name='purchaseorder',
            name='receipt_reminder_email',
            field=models.BooleanField(default=False, help_text='Receipt Reminder Email'),
        ),
        migrations.AddField(
            model_name='purchaseorder',
            name='reminder_date_before_receipt',
            field=models.IntegerField(default=1, help_text='Days Before Receipt'),
        ),
        migrations.AddField(
            model_name='purchaseorder',
            name='tax_country_id',
            field=models.ForeignKey(blank=True, help_text='Tax Country', null=True, on_delete=django.db.models.deletion.SET_NULL, to='core.country'),
        ),
        migrations.AddField(
            model_name='purchaseorderline',
            name='product_id',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='inventory.product'),
        ),
        migrations.AddField(
            model_name='purchaseorderline',
            name='product_packaging_id',
            field=models.ForeignKey(blank=True, help_text='Product Packaging', null=True, on_delete=django.db.models.deletion.PROTECT, to='inventory.productpackaging'),
        ),
        migrations.AddField(
            model_name='purchaseorderline',
            name='product_packaging_qty',
            field=models.FloatField(default=0.0, help_text='Packaging Quantity'),
        ),
        migrations.AddField(
            model_name='purchaseorderline',
            name='product_template_id',
            field=models.ForeignKey(blank=True, help_text='Product Template', null=True, on_delete=django.db.models.deletion.PROTECT, to='inventory.producttemplate'),
        ),
        migrations.AddField(
            model_name='purchaseorderline',
            name='qty_received_manual',
            field=models.FloatField(default=0.0, help_text='Manual Received Quantity'),
        ),
        migrations.AddField(
            model_name='purchaseorderline',
            name='qty_received_method',
            field=models.CharField(choices=[('manual', 'Manual'), ('stock_moves', 'Stock Moves')], default='stock_moves', help_text='Method to update received quantity', max_length=20),
        ),
        migrations.AlterField(
            model_name='purchaseorder',
            name='mail_reminder_confirmed',
            field=models.BooleanField(default=False, help_text='Reminder Confirmed'),
        ),
        migrations.AlterField(
            model_name='purchaseorderline',
            name='display_type',
            field=models.CharField(blank=True, choices=[('line_section', 'Section'), ('line_note', 'Note')], help_text='Line display type', max_length=20, null=True),
        ),
        migrations.AddIndex(
            model_name='purchaseorderline',
            index=models.Index(fields=['product_id'], name='purchases_p_product_79ceb0_idx'),
        ),
        migrations.AddConstraint(
            model_name='purchaseorderline',
            constraint=models.CheckConstraint(check=models.Q(('display_type__isnull', False), models.Q(('product_id__isnull', False), ('product_uom__isnull', False), ('date_planned__isnull', False)), _connector='OR'), name='purchase_accountable_required_fields'),
        ),
        migrations.AddConstraint(
            model_name='purchaseorderline',
            constraint=models.CheckConstraint(check=models.Q(('display_type__isnull', True), models.Q(('product_id__isnull', True), ('price_unit', 0), ('product_qty', 0), ('product_uom__isnull', True), ('date_planned__isnull', True)), _connector='OR'), name='purchase_non_accountable_null_fields'),
        ),
    ]
