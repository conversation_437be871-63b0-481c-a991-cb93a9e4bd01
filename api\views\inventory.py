from rest_framework import viewsets, serializers
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

# Placeholder serializer for API documentation
class InventoryPlaceholderSerializer(serializers.Serializer):
    message = serializers.CharField(default="This endpoint is not yet implemented")

# Placeholder for inventory API views
# TODO: Implement full inventory API

class ProductViewSet(viewsets.ViewSet):
    permission_classes = [IsAuthenticated]
    serializer_class = InventoryPlaceholderSerializer

    def list(self, request):
        return Response({"message": "Product API endpoints will be implemented soon"})

class ProductTemplateViewSet(viewsets.ViewSet):
    permission_classes = [IsAuthenticated]
    serializer_class = InventoryPlaceholderSerializer

    def list(self, request):
        return Response({"message": "Product Template API endpoints will be implemented soon"})

class StockMoveViewSet(viewsets.ViewSet):
    permission_classes = [IsAuthenticated]
    serializer_class = InventoryPlaceholderSerializer

    def list(self, request):
        return Response({"message": "Stock Move API endpoints will be implemented soon"})

class StockPickingViewSet(viewsets.ViewSet):
    permission_classes = [IsAuthenticated]
    serializer_class = InventoryPlaceholderSerializer

    def list(self, request):
        return Response({"message": "Stock Picking API endpoints will be implemented soon"})
