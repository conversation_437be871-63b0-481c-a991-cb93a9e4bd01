# Generated by Django 4.2.21 on 2025-07-21 13:20

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0005_stockmove_is_done'),
        ('mrp', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='stockmove',
            name='production_id',
            field=models.ForeignKey(blank=True, help_text='Manufacturing Order (Finished Product)', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='move_finished_ids', to='mrp.mrpproduction'),
        ),
        migrations.AddField(
            model_name='stockmove',
            name='raw_material_production_id',
            field=models.ForeignKey(blank=True, help_text='Manufacturing Order (Raw Material)', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='move_raw_ids', to='mrp.mrpproduction'),
        ),
        migrations.AddField(
            model_name='stockmove',
            name='unbuild_id',
            field=models.ForeignKey(blank=True, help_text='Unbuild Order', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='move_ids', to='mrp.mrpunbuild'),
        ),
        migrations.AddField(
            model_name='stockmove',
            name='unit_factor',
            field=models.DecimalField(decimal_places=6, default=1.0, help_text='Unit Factor (for MRP)', max_digits=12),
        ),
        migrations.AddField(
            model_name='stockmove',
            name='workorder_id',
            field=models.ForeignKey(blank=True, help_text='Work Order', null=True, on_delete=django.db.models.deletion.SET_NULL, to='mrp.mrpworkorder'),
        ),
        migrations.AddIndex(
            model_name='stockmove',
            index=models.Index(fields=['production_id'], name='inventory_s_product_7b900b_idx'),
        ),
        migrations.AddIndex(
            model_name='stockmove',
            index=models.Index(fields=['raw_material_production_id'], name='inventory_s_raw_mat_c2bc3b_idx'),
        ),
        migrations.AddIndex(
            model_name='stockmove',
            index=models.Index(fields=['is_done'], name='inventory_s_is_done_e6a029_idx'),
        ),
    ]
