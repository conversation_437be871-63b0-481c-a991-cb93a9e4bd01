from django.test import TestCase
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from decimal import Decimal
from datetime import timed<PERSON>ta
from django.utils import timezone
from core.models import Company, Country, Currency, Partner
from accounting.models import AccountAccount
from sales.models import ProductUom, ProductUomCategory, StockWarehouse
from purchases.models import StockPickingType
from .models import (
    ProductCategory, ProductTemplate, Product, StockLocation,
    StockPicking, StockMove, StockQuant, StockLot, StockInventory,
    StockInventoryLine, StockRemovalStrategy, IrSequence
)

class InventoryModelsTestCase(TestCase):
    def setUp(self):
        """Set up test data"""
        # Create user
        self.user = User.objects.create_user(
            username='inventoryuser',
            email='<EMAIL>',
            password='testpass123'
        )

        # Create country and currency
        self.country = Country.objects.create(
            name='United States',
            code='US',
            create_uid=self.user,
            write_uid=self.user
        )

        self.currency = Currency.objects.create(
            name='USD',
            symbol='$',
            create_uid=self.user,
            write_uid=self.user
        )

        # Create company
        self.company = Company.objects.create(
            name='Test Inventory Company',
            code='TIC',
            currency_id=self.currency,
            country_id=self.country,
            create_uid=self.user,
            write_uid=self.user
        )

        # Create partner
        self.partner = Partner.objects.create(
            name='Test Partner',
            create_uid=self.user,
            write_uid=self.user
        )

        # Create UOM category and UOM
        self.uom_category = ProductUomCategory.objects.create(
            name='Unit',
            create_uid=self.user,
            write_uid=self.user
        )

        self.uom_unit = ProductUom.objects.create(
            name='Unit',
            category_id=self.uom_category,
            uom_type='reference',
            create_uid=self.user,
            write_uid=self.user
        )

        # Create product category
        self.product_category = ProductCategory.objects.create(
            name='Test Category',
            property_valuation='real_time',
            property_cost_method='fifo',
            create_uid=self.user,
            write_uid=self.user
        )

        # Create warehouse
        self.warehouse = StockWarehouse.objects.create(
            name='Main Warehouse',
            code='WH',
            company_id=self.company,
            partner_id=self.partner,
            create_uid=self.user,
            write_uid=self.user
        )

        # Create locations
        self.location_stock = StockLocation.objects.create(
            name='Stock',
            usage='internal',
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )

        self.location_customers = StockLocation.objects.create(
            name='Customers',
            usage='customer',
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )

        self.location_suppliers = StockLocation.objects.create(
            name='Vendors',
            usage='supplier',
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )

        # Create picking type
        self.picking_type = StockPickingType.objects.create(
            name='Receipts',
            code='incoming',
            warehouse_id=self.warehouse,
            create_uid=self.user,
            write_uid=self.user
        )

        # Create sequence
        self.sequence = IrSequence.objects.create(
            name='Stock Picking Sequence',
            code='stock.picking',
            prefix='IN/',
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )

    def test_product_category_creation(self):
        """Test product category creation and hierarchy"""
        # Create parent category
        parent_category = ProductCategory.objects.create(
            name='Parent Category',
            create_uid=self.user,
            write_uid=self.user
        )

        # Create child category
        child_category = ProductCategory.objects.create(
            name='Child Category',
            parent_id=parent_category,
            create_uid=self.user,
            write_uid=self.user
        )

        # Check complete name computation
        self.assertEqual(child_category.complete_name, 'Parent Category / Child Category')
        self.assertTrue(child_category.parent_path.endswith(str(parent_category.id)))

    def test_product_template_creation(self):
        """Test product template creation and validation"""
        # Create product template
        template = ProductTemplate.objects.create(
            name='Test Product Template',
            default_code='TEST001',
            detailed_type='product',
            categ_id=self.product_category,
            list_price=Decimal('100.0'),
            standard_price=Decimal('50.0'),
            uom_id=self.uom_unit,
            uom_po_id=self.uom_unit,
            tracking='lot',
            create_uid=self.user,
            write_uid=self.user
        )

        self.assertEqual(template.name, 'Test Product Template')
        self.assertEqual(template.detailed_type, 'product')
        self.assertEqual(template.tracking, 'lot')
        self.assertEqual(str(template), 'Test Product Template')

    def test_product_variant_creation(self):
        """Test product variant creation"""
        # Create product template
        template = ProductTemplate.objects.create(
            name='Test Product',
            detailed_type='product',
            categ_id=self.product_category,
            uom_id=self.uom_unit,
            uom_po_id=self.uom_unit,
            create_uid=self.user,
            write_uid=self.user
        )

        # Create product variant
        product = Product.objects.create(
            product_tmpl_id=template,
            default_code='PROD001',
            create_uid=self.user,
            write_uid=self.user
        )

        # Test properties from template
        self.assertEqual(product.name, 'Test Product')
        self.assertEqual(product.detailed_type, 'product')
        self.assertEqual(product.display_name, '[PROD001] Test Product')
        self.assertEqual(product.uom_id, self.uom_unit)

    def test_stock_location_hierarchy(self):
        """Test stock location hierarchy"""
        # Create parent location
        parent_location = StockLocation.objects.create(
            name='Warehouse',
            usage='view',
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )

        # Create child location
        child_location = StockLocation.objects.create(
            name='Shelf A',
            usage='internal',
            location_id=parent_location,
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )

        # Check complete name and path
        self.assertEqual(child_location.complete_name, 'Warehouse/Shelf A')
        self.assertTrue(child_location.parent_path.endswith(str(parent_location.id)))
        self.assertTrue(child_location._should_be_valued())
        self.assertFalse(parent_location._should_be_valued())  # View locations not valued

    def test_stock_picking_creation(self):
        """Test stock picking creation and workflow"""
        # Create product
        template = ProductTemplate.objects.create(
            name='Test Product',
            detailed_type='product',
            categ_id=self.product_category,
            uom_id=self.uom_unit,
            uom_po_id=self.uom_unit,
            create_uid=self.user,
            write_uid=self.user
        )

        product = Product.objects.create(
            product_tmpl_id=template,
            create_uid=self.user,
            write_uid=self.user
        )

        # Create picking
        picking = StockPicking.objects.create(
            partner_id=self.partner,
            location_id=self.location_suppliers,
            location_dest_id=self.location_stock,
            picking_type_id=self.picking_type,
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )

        # Create move
        move = StockMove.objects.create(
            name='Test Move',
            product_id=product,
            product_uom_qty=Decimal('10.0'),
            product_uom=self.uom_unit,
            location_id=self.location_suppliers,
            location_dest_id=self.location_stock,
            picking_id=picking,
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )

        # Test initial state
        self.assertEqual(picking.state, 'draft')
        self.assertEqual(move.state, 'draft')
        self.assertEqual(str(picking), 'New')

        # Confirm picking
        picking.action_confirm()

        self.assertEqual(picking.state, 'confirmed')
        self.assertNotEqual(picking.name, 'New')

        # Assign picking
        picking.action_assign()

        # Should be assigned since supplier location has unlimited stock
        self.assertEqual(picking.state, 'assigned')

        # Validate picking
        picking.button_validate()

        self.assertEqual(picking.state, 'done')
        self.assertIsNotNone(picking.date_done)

    def test_stock_move_workflow(self):
        """Test stock move workflow"""
        # Create product
        template = ProductTemplate.objects.create(
            name='Test Product',
            detailed_type='product',
            categ_id=self.product_category,
            uom_id=self.uom_unit,
            uom_po_id=self.uom_unit,
            create_uid=self.user,
            write_uid=self.user
        )

        product = Product.objects.create(
            product_tmpl_id=template,
            create_uid=self.user,
            write_uid=self.user
        )

        # Create move
        move = StockMove.objects.create(
            name='Test Move',
            product_id=product,
            product_uom_qty=Decimal('5.0'),
            product_uom=self.uom_unit,
            location_id=self.location_suppliers,
            location_dest_id=self.location_stock,
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )

        # Test move workflow
        self.assertEqual(move.state, 'draft')

        move._action_confirm()
        self.assertEqual(move.state, 'confirmed')

        move._action_assign()
        self.assertEqual(move.state, 'assigned')  # Supplier has unlimited stock

        move._action_done()
        self.assertEqual(move.state, 'done')
        self.assertEqual(move.quantity_done, Decimal('5.0'))

    def test_stock_quant_creation(self):
        """Test stock quant creation and computation"""
        # Create product
        template = ProductTemplate.objects.create(
            name='Test Product',
            detailed_type='product',
            categ_id=self.product_category,
            uom_id=self.uom_unit,
            uom_po_id=self.uom_unit,
            create_uid=self.user,
            write_uid=self.user
        )

        product = Product.objects.create(
            product_tmpl_id=template,
            create_uid=self.user,
            write_uid=self.user
        )

        # Create stock quant
        quant = StockQuant.objects.create(
            product_id=product,
            location_id=self.location_stock,
            quantity=Decimal('100.0'),
            reserved_quantity=Decimal('20.0'),
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )

        # Check computed available quantity
        self.assertEqual(quant.available_quantity, Decimal('80.0'))
        self.assertEqual(str(quant), f"{product.display_name} @ {self.location_stock.name}: 100.0")

    def test_stock_lot_creation(self):
        """Test lot/serial number creation"""
        # Create product with lot tracking
        template = ProductTemplate.objects.create(
            name='Tracked Product',
            detailed_type='product',
            categ_id=self.product_category,
            uom_id=self.uom_unit,
            uom_po_id=self.uom_unit,
            tracking='lot',
            create_uid=self.user,
            write_uid=self.user
        )

        product = Product.objects.create(
            product_tmpl_id=template,
            create_uid=self.user,
            write_uid=self.user
        )

        # Create lot
        lot = StockLot.objects.create(
            name='LOT001',
            product_id=product,
            company_id=self.company,
            use_date=timezone.now().date() + timedelta(days=30),
            create_uid=self.user,
            write_uid=self.user
        )

        self.assertEqual(lot.name, 'LOT001')
        self.assertEqual(str(lot), f"LOT001 ({product.display_name})")

    def test_stock_inventory_workflow(self):
        """Test inventory adjustment workflow"""
        # Create product
        template = ProductTemplate.objects.create(
            name='Inventory Product',
            detailed_type='product',
            categ_id=self.product_category,
            uom_id=self.uom_unit,
            uom_po_id=self.uom_unit,
            create_uid=self.user,
            write_uid=self.user
        )

        product = Product.objects.create(
            product_tmpl_id=template,
            create_uid=self.user,
            write_uid=self.user
        )

        # Create inventory
        inventory = StockInventory.objects.create(
            name='Annual Inventory',
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )

        # Add locations and products
        inventory.location_ids.add(self.location_stock)
        inventory.product_ids.add(product)

        # Test initial state
        self.assertEqual(inventory.state, 'draft')
        self.assertEqual(str(inventory), 'Annual Inventory')

        # Start inventory
        inventory.action_start()
        self.assertEqual(inventory.state, 'confirm')

        # Create inventory line
        line = StockInventoryLine.objects.create(
            inventory_id=inventory,
            product_id=product,
            location_id=self.location_stock,
            theoretical_qty=Decimal('50.0'),
            product_qty=Decimal('45.0'),  # 5 units missing
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )

        # Check difference computation
        self.assertEqual(line.difference_qty, Decimal('-5.0'))

        # Validate inventory
        inventory.action_validate()
        self.assertEqual(inventory.state, 'done')

    def test_sequence_generation(self):
        """Test sequence number generation"""
        # Test sequence generation
        number1 = self.sequence.next_by_code('stock.picking')
        number2 = self.sequence.next_by_code('stock.picking')

        self.assertEqual(number1, 'IN/0001')
        self.assertEqual(number2, 'IN/0002')

        # Check sequence increment
        self.sequence.refresh_from_db()
        self.assertEqual(self.sequence.number_next, 3)

    def test_product_tracking_validation(self):
        """Test product tracking validation"""
        # Test invalid tracking for storable product
        with self.assertRaises(ValidationError):
            template = ProductTemplate(
                name='Invalid Product',
                detailed_type='product',
                categ_id=self.product_category,
                uom_id=self.uom_unit,
                uom_po_id=self.uom_unit,
                tracking='invalid',  # Invalid tracking
                create_uid=self.user,
                write_uid=self.user
            )
            template.full_clean()

    def test_uom_consistency_validation(self):
        """Test UOM consistency validation"""
        # Create different UOM category
        weight_category = ProductUomCategory.objects.create(
            name='Weight',
            create_uid=self.user,
            write_uid=self.user
        )

        uom_kg = ProductUom.objects.create(
            name='kg',
            category_id=weight_category,
            uom_type='reference',
            create_uid=self.user,
            write_uid=self.user
        )

        # Test UOM category mismatch
        with self.assertRaises(ValidationError):
            template = ProductTemplate(
                name='Invalid UOM Product',
                detailed_type='product',
                categ_id=self.product_category,
                uom_id=self.uom_unit,  # Unit category
                uom_po_id=uom_kg,      # Weight category - mismatch!
                create_uid=self.user,
                write_uid=self.user
            )
            template.full_clean()

    def test_removal_strategy(self):
        """Test removal strategy functionality"""
        strategy = StockRemovalStrategy.objects.create(
            name='FIFO Strategy',
            method='fifo',
            create_uid=self.user,
            write_uid=self.user
        )

        self.assertEqual(strategy.method, 'fifo')
        self.assertEqual(str(strategy), 'FIFO Strategy')

    def test_picking_cancel_validation(self):
        """Test picking cancellation validation"""
        # Create picking
        picking = StockPicking.objects.create(
            location_id=self.location_suppliers,
            location_dest_id=self.location_stock,
            picking_type_id=self.picking_type,
            company_id=self.company,
            state='done',  # Already done
            create_uid=self.user,
            write_uid=self.user
        )

        # Should not be able to cancel done picking
        with self.assertRaises(ValidationError):
            picking.action_cancel()

    def test_move_cancel_validation(self):
        """Test move cancellation validation"""
        # Create product
        template = ProductTemplate.objects.create(
            name='Test Product',
            detailed_type='product',
            categ_id=self.product_category,
            uom_id=self.uom_unit,
            uom_po_id=self.uom_unit,
            create_uid=self.user,
            write_uid=self.user
        )

        product = Product.objects.create(
            product_tmpl_id=template,
            create_uid=self.user,
            write_uid=self.user
        )

        # Create done move
        move = StockMove.objects.create(
            name='Done Move',
            product_id=product,
            product_uom_qty=Decimal('1.0'),
            product_uom=self.uom_unit,
            location_id=self.location_suppliers,
            location_dest_id=self.location_stock,
            company_id=self.company,
            state='done',
            create_uid=self.user,
            write_uid=self.user
        )

        # Should not be able to cancel done move
        with self.assertRaises(ValidationError):
            move._action_cancel()
