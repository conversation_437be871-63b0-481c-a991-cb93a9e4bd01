"""
Management command to load currencies from Odoo data
"""

import json
import os
from django.core.management.base import BaseCommand
from django.db import transaction
from django.contrib.auth import get_user_model
from core.models import Currency

User = get_user_model()


class Command(BaseCommand):
    help = 'Load standard currencies with exchange rates'

    def add_arguments(self, parser):
        parser.add_argument(
            '--update',
            action='store_true',
            help='Update existing currencies',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Loading currencies from Odoo data...'))

        # Load currencies from extracted JSON file
        json_file = 'currencies_data.json'
        if not os.path.exists(json_file):
            self.stdout.write(
                self.style.ERROR(
                    f'Currency data file {json_file} not found. '
                    'Please run: python extract_odoo_data.py first'
                )
            )
            return

        with open(json_file, 'r') as f:
            currencies_data = json.load(f)


        created_count = 0
        updated_count = 0

        # Get or create admin user for create_uid/write_uid
        admin_user, created = User.objects.get_or_create(
            username='admin',
            defaults={'email': '<EMAIL>', 'is_staff': True, 'is_superuser': True}
        )

        with transaction.atomic():
            for currency_data in currencies_data:
                iso_code = currency_data['name']  # In Odoo, name is the ISO code

                try:
                    currency = Currency.objects.get(name=iso_code)
                    if options['update']:
                        # Update existing currency
                        for field, value in currency_data.items():
                            setattr(currency, field, value)
                        currency.save()
                        updated_count += 1
                        self.stdout.write(f'Updated currency: {currency.full_name} ({iso_code})')
                    else:
                        self.stdout.write(f'Currency already exists: {currency.full_name} ({iso_code})')

                except Currency.DoesNotExist:
                    # Create new currency with required user fields
                    currency_data['create_uid'] = admin_user
                    currency_data['write_uid'] = admin_user
                    currency = Currency.objects.create(**currency_data)
                    created_count += 1
                    self.stdout.write(f'Created currency: {currency.full_name} ({iso_code})')

        # Summary
        self.stdout.write(
            self.style.SUCCESS(
                f'\nCurrency loading completed!\n'
                f'Created: {created_count} currencies\n'
                f'Updated: {updated_count} currencies\n'
                f'Total currencies in system: {Currency.objects.count()}'
            )
        )
        
        # Show loaded currencies
        self.stdout.write('\nLoaded currencies:')
        for currency in Currency.objects.filter(active=True).order_by('name'):
            self.stdout.write(f'  {currency.name} - {currency.full_name} ({currency.symbol})')
