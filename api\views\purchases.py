from rest_framework import viewsets, serializers
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

# Placeholder serializer for API documentation
class PurchasePlaceholderSerializer(serializers.Serializer):
    message = serializers.CharField(default="This endpoint is not yet implemented")

# Placeholder for purchase API views
# TODO: Implement full purchase API

class PurchaseOrderViewSet(viewsets.ViewSet):
    permission_classes = [IsAuthenticated]
    serializer_class = PurchasePlaceholderSerializer

    def list(self, request):
        return Response({"message": "Purchase Order API endpoints will be implemented soon"})

class PurchaseOrderLineViewSet(viewsets.ViewSet):
    permission_classes = [IsAuthenticated]
    serializer_class = PurchasePlaceholderSerializer

    def list(self, request):
        return Response({"message": "Purchase Order Line API endpoints will be implemented soon"})
