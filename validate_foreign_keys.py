#!/usr/bin/env python
"""
Foreign Key Validation Script
Validates that all foreign key relationships are properly resolved
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'django_erp.settings')
django.setup()

from django.apps import apps
from django.db import models
from django.core.exceptions import FieldDoesNotExist

def validate_foreign_keys():
    """Validate all foreign key relationships in the Django ERP system"""
    
    print("🔍 Django ERP Foreign Key Validation")
    print("=" * 50)
    
    total_fks = 0
    valid_fks = 0
    invalid_fks = []
    
    # Get all models from our ERP apps
    erp_apps = ['core', 'accounting', 'sales', 'purchases', 'inventory']
    
    for app_name in erp_apps:
        print(f"\n📦 Validating {app_name.upper()} module...")
        
        try:
            app_config = apps.get_app_config(app_name)
            models_list = app_config.get_models()
            
            for model in models_list:
                print(f"  📋 {model.__name__}")
                
                # Check all fields in the model
                for field in model._meta.get_fields():
                    if isinstance(field, (models.ForeignKey, models.OneToOneField)):
                        total_fks += 1
                        
                        try:
                            # Try to get the related model
                            related_model = field.related_model
                            
                            if related_model:
                                print(f"    ✅ {field.name} → {related_model._meta.label}")
                                valid_fks += 1
                            else:
                                print(f"    ❌ {field.name} → UNRESOLVED")
                                invalid_fks.append(f"{model._meta.label}.{field.name}")
                                
                        except Exception as e:
                            print(f"    ❌ {field.name} → ERROR: {e}")
                            invalid_fks.append(f"{model._meta.label}.{field.name}")
                    
                    elif isinstance(field, models.ManyToManyField):
                        total_fks += 1
                        
                        try:
                            related_model = field.related_model
                            if related_model:
                                print(f"    ✅ {field.name} → {related_model._meta.label} (M2M)")
                                valid_fks += 1
                            else:
                                print(f"    ❌ {field.name} → UNRESOLVED (M2M)")
                                invalid_fks.append(f"{model._meta.label}.{field.name}")
                                
                        except Exception as e:
                            print(f"    ❌ {field.name} → ERROR: {e} (M2M)")
                            invalid_fks.append(f"{model._meta.label}.{field.name}")
                            
        except Exception as e:
            print(f"  ❌ Error loading {app_name}: {e}")
    
    # Print summary
    print("\n" + "=" * 50)
    print("📊 VALIDATION SUMMARY")
    print("=" * 50)
    print(f"Total Foreign Keys: {total_fks}")
    print(f"Valid Foreign Keys: {valid_fks}")
    print(f"Invalid Foreign Keys: {len(invalid_fks)}")
    
    if invalid_fks:
        print("\n❌ INVALID FOREIGN KEYS:")
        for fk in invalid_fks:
            print(f"  - {fk}")
        print(f"\n🚨 RESULT: {len(invalid_fks)} foreign key issues found!")
        return False
    else:
        print("\n✅ RESULT: ALL FOREIGN KEYS VALID!")
        print("🎉 No foreign key dependency issues found!")
        return True

def validate_imports():
    """Validate that all models can be imported without errors"""
    
    print("\n🔍 Import Validation")
    print("=" * 30)
    
    import_tests = [
        ("Core Models", "from core.models import Company, Partner, Currency, Country"),
        ("Accounting Models", "from accounting.models import AccountMove, AccountMoveLine, AccountJournal, AccountAccount"),
        ("Sales Models", "from sales.models import SaleOrder, SaleOrderLine, SalesTeam, ProductPricelist"),
        ("Purchase Models", "from purchases.models import PurchaseOrder, PurchaseOrderLine, PurchaseApprovalSettings"),
        ("Inventory Models", "from inventory.models import Product, ProductTemplate, StockMove, StockPicking"),
    ]
    
    all_imports_valid = True
    
    for test_name, import_statement in import_tests:
        try:
            exec(import_statement)
            print(f"✅ {test_name}")
        except Exception as e:
            print(f"❌ {test_name}: {e}")
            all_imports_valid = False
    
    return all_imports_valid

def validate_model_relationships():
    """Validate specific critical relationships"""
    
    print("\n🔍 Critical Relationship Validation")
    print("=" * 40)
    
    try:
        from core.models import Company, Partner
        from accounting.models import AccountMove
        from sales.models import SaleOrder
        from purchases.models import PurchaseOrder
        from inventory.models import Product
        
        # Test critical relationships
        relationships = [
            ("AccountMove → Company", lambda: AccountMove._meta.get_field('company_id').related_model == Company),
            ("AccountMove → Partner", lambda: AccountMove._meta.get_field('partner_id').related_model == Partner),
            ("SaleOrder → Company", lambda: SaleOrder._meta.get_field('company_id').related_model == Company),
            ("SaleOrder → Partner", lambda: SaleOrder._meta.get_field('partner_id').related_model == Partner),
            ("PurchaseOrder → Company", lambda: PurchaseOrder._meta.get_field('company_id').related_model == Company),
            ("PurchaseOrder → Partner", lambda: PurchaseOrder._meta.get_field('partner_id').related_model == Partner),
        ]
        
        all_valid = True
        for rel_name, test_func in relationships:
            try:
                if test_func():
                    print(f"✅ {rel_name}")
                else:
                    print(f"❌ {rel_name}")
                    all_valid = False
            except Exception as e:
                print(f"❌ {rel_name}: {e}")
                all_valid = False
        
        return all_valid
        
    except Exception as e:
        print(f"❌ Error in relationship validation: {e}")
        return False

def main():
    """Main validation function"""
    
    print("🚀 Django ERP Foreign Key Validation Suite")
    print("=" * 60)
    
    # Run all validations
    fk_valid = validate_foreign_keys()
    import_valid = validate_imports()
    rel_valid = validate_model_relationships()
    
    # Final result
    print("\n" + "=" * 60)
    print("🏆 FINAL VALIDATION RESULT")
    print("=" * 60)
    
    if fk_valid and import_valid and rel_valid:
        print("✅ ALL VALIDATIONS PASSED!")
        print("🎉 Django ERP is ready for deployment!")
        print("📊 Foreign key dependency issues: RESOLVED")
        return True
    else:
        print("❌ SOME VALIDATIONS FAILED!")
        print("🚨 Please review the issues above")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
