from django.test import TestCase
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from decimal import Decimal
from datetime import timedelta
from django.utils import timezone
from core.models import Company, Country, Currency, Partner
from .models import (
    CrmTeam, CrmStage, CrmLostReason, CrmLead, 
    CrmActivityType, CrmActivity, CrmTag
)


class CrmModelsTestCase(TestCase):
    def setUp(self):
        """Set up test data"""
        # Create user
        self.user = User.objects.create_user(
            username='crmuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.salesperson = User.objects.create_user(
            username='salesperson',
            email='<EMAIL>',
            password='testpass123'
        )

        # Create country and currency
        self.country = Country.objects.create(
            name='United States',
            code='US',
            create_uid=self.user,
            write_uid=self.user
        )

        self.currency = Currency.objects.create(
            name='USD',
            symbol='$',
            create_uid=self.user,
            write_uid=self.user
        )

        # Create company
        self.company = Company.objects.create(
            name='Test CRM Company',
            code='TCC',
            currency_id=self.currency,
            country_id=self.country,
            create_uid=self.user,
            write_uid=self.user
        )

        # Create partner
        self.partner = Partner.objects.create(
            name='Test Customer',
            customer_rank=1,  # Makes is_customer = True
            email='<EMAIL>',
            phone='+1234567890',
            create_uid=self.user,
            write_uid=self.user
        )

        # Create CRM team
        self.crm_team = CrmTeam.objects.create(
            name='Sales Team',
            company_id=self.company,
            user_id=self.user,
            use_leads=True,
            use_opportunities=True,
            create_uid=self.user,
            write_uid=self.user
        )
        self.crm_team.member_ids.add(self.salesperson)

        # Create CRM stages
        self.stage_new = CrmStage.objects.create(
            name='New',
            sequence=1,
            probability=10.0,
            create_uid=self.user,
            write_uid=self.user
        )
        
        self.stage_qualified = CrmStage.objects.create(
            name='Qualified',
            sequence=2,
            probability=25.0,
            create_uid=self.user,
            write_uid=self.user
        )
        
        self.stage_won = CrmStage.objects.create(
            name='Won',
            sequence=10,
            probability=100.0,
            is_won=True,
            create_uid=self.user,
            write_uid=self.user
        )

        self.stage_lost = CrmStage.objects.create(
            name='Lost',
            sequence=11,
            probability=0.0,
            create_uid=self.user,
            write_uid=self.user
        )

        # Create lost reason
        self.lost_reason = CrmLostReason.objects.create(
            name='Price too high',
            create_uid=self.user,
            write_uid=self.user
        )

        # Create activity type
        self.activity_type = CrmActivityType.objects.create(
            name='Call',
            category='phonecall',
            delay_count=1,
            delay_unit='days',
            create_uid=self.user,
            write_uid=self.user
        )

    def test_crm_team_creation(self):
        """Test CRM team creation and validation"""
        team = CrmTeam.objects.create(
            name='New Sales Team',
            company_id=self.company,
            invoiced_target=Decimal('50000.0'),
            create_uid=self.user,
            write_uid=self.user
        )
        
        self.assertEqual(team.name, 'New Sales Team')
        self.assertEqual(team.company_id, self.company)
        self.assertEqual(team.invoiced_target, Decimal('50000.0'))
        self.assertTrue(team.use_leads)
        self.assertTrue(team.use_opportunities)

    def test_crm_stage_probability_constraint(self):
        """Test stage probability constraint"""
        # Valid probability
        stage = CrmStage.objects.create(
            name='Test Stage',
            probability=50.0,
            create_uid=self.user,
            write_uid=self.user
        )
        self.assertEqual(stage.probability, 50.0)

        # Invalid probability should be caught by constraint
        with self.assertRaises(ValidationError):
            stage = CrmStage(
                name='Invalid Stage',
                probability=150.0,  # Invalid: > 100
                create_uid=self.user,
                write_uid=self.user
            )
            stage.full_clean()

    def test_lead_creation_and_validation(self):
        """Test lead creation and validation"""
        lead = CrmLead.objects.create(
            name='Test Lead',
            type='lead',
            partner_name='Prospect Company',
            contact_name='John Doe',
            email_from='<EMAIL>',
            phone='+1987654321',
            user_id=self.salesperson,
            team_id=self.crm_team,
            stage_id=self.stage_new,
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )
        
        self.assertEqual(lead.name, 'Test Lead')
        self.assertEqual(lead.type, 'lead')
        self.assertEqual(lead.partner_name, 'Prospect Company')
        self.assertEqual(lead.probability, 10.0)  # From stage
        self.assertEqual(lead.currency_id, self.currency)  # From company

    def test_opportunity_creation_and_validation(self):
        """Test opportunity creation and validation"""
        opportunity = CrmLead.objects.create(
            name='Test Opportunity',
            type='opportunity',
            partner_id=self.partner,
            expected_revenue=Decimal('10000.0'),
            user_id=self.salesperson,
            team_id=self.crm_team,
            stage_id=self.stage_qualified,
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )
        
        self.assertEqual(opportunity.type, 'opportunity')
        self.assertEqual(opportunity.expected_revenue, Decimal('10000.0'))
        self.assertEqual(opportunity.probability, 25.0)  # From stage
        self.assertEqual(opportunity.prorated_revenue, Decimal('2500.0'))  # 25% of 10000

    def test_lead_to_opportunity_conversion(self):
        """Test converting lead to opportunity"""
        lead = CrmLead.objects.create(
            name='Lead to Convert',
            type='lead',
            partner_name='Convert Company',
            user_id=self.salesperson,
            team_id=self.crm_team,
            stage_id=self.stage_new,
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )
        
        # Convert to opportunity
        lead.convert_to_opportunity()
        
        self.assertEqual(lead.type, 'opportunity')
        self.assertIsNotNone(lead.date_conversion)

    def test_opportunity_won_lost_actions(self):
        """Test marking opportunity as won/lost"""
        opportunity = CrmLead.objects.create(
            name='Test Opportunity Actions',
            type='opportunity',
            expected_revenue=Decimal('5000.0'),
            user_id=self.salesperson,
            team_id=self.crm_team,
            stage_id=self.stage_qualified,
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )
        
        # Test marking as won
        opportunity.action_set_won()
        self.assertEqual(opportunity.stage_id, self.stage_won)
        self.assertEqual(opportunity.probability, 100.0)
        self.assertIsNotNone(opportunity.date_closed)
        
        # Reset for lost test
        opportunity.date_closed = None
        opportunity.save()
        
        # Test marking as lost
        opportunity.action_set_lost(self.lost_reason.id)
        self.assertEqual(opportunity.probability, 0.0)
        self.assertIsNotNone(opportunity.date_closed)

    def test_activity_creation_and_state_management(self):
        """Test activity creation and state management"""
        lead = CrmLead.objects.create(
            name='Lead with Activity',
            type='lead',
            user_id=self.salesperson,
            team_id=self.crm_team,
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )
        
        # Create activity for tomorrow (planned)
        tomorrow = timezone.now().date() + timedelta(days=1)
        activity = CrmActivity.objects.create(
            summary='Call prospect',
            activity_type_id=self.activity_type,
            user_id=self.salesperson,
            lead_id=lead,
            res_id=1,  # Required field - use simple ID
            date_deadline=tomorrow,
            create_uid=self.user,
            write_uid=self.user
        )
        
        self.assertEqual(activity.state, 'planned')
        
        # Mark as done
        activity.action_done(feedback='Called and left message')
        self.assertEqual(activity.state, 'done')
        self.assertIsNotNone(activity.date_done)
        self.assertEqual(activity.feedback, 'Called and left message')

    def test_activity_overdue_state(self):
        """Test activity overdue state calculation"""
        lead = CrmLead.objects.create(
            name='Lead with Overdue Activity',
            type='lead',
            user_id=self.salesperson,
            team_id=self.crm_team,
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )
        
        # Create activity for yesterday (overdue)
        yesterday = timezone.now().date() - timedelta(days=1)
        activity = CrmActivity.objects.create(
            summary='Overdue call',
            activity_type_id=self.activity_type,
            user_id=self.salesperson,
            lead_id=lead,
            res_id=1,  # Required field - use simple ID
            date_deadline=yesterday,
            create_uid=self.user,
            write_uid=self.user
        )
        
        self.assertEqual(activity.state, 'overdue')

    def test_crm_tag_creation(self):
        """Test CRM tag creation"""
        tag = CrmTag.objects.create(
            name='Hot Lead',
            color=1,
            create_uid=self.user,
            write_uid=self.user
        )
        
        self.assertEqual(tag.name, 'Hot Lead')
        self.assertEqual(tag.color, 1)

    def test_revenue_calculations(self):
        """Test revenue calculations"""
        opportunity = CrmLead.objects.create(
            name='Revenue Test',
            type='opportunity',
            expected_revenue=Decimal('20000.0'),
            recurring_revenue=Decimal('2000.0'),
            user_id=self.salesperson,
            team_id=self.crm_team,
            stage_id=self.stage_qualified,  # 25% probability
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )
        
        # Check prorated revenue calculation
        self.assertEqual(opportunity.prorated_revenue, Decimal('5000.0'))  # 25% of 20000
        
        # Update stage and check recalculation
        opportunity.stage_id = self.stage_won  # 100% probability
        opportunity.save()
        
        self.assertEqual(opportunity.probability, 100.0)
        self.assertEqual(opportunity.prorated_revenue, Decimal('20000.0'))  # 100% of 20000
